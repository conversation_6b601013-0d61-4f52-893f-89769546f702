/**
 * 移动端视频标题背景高度自适应调整器
 * 根据视频标题的行数动态调整背景高度，确保所有文字都被背景覆盖
 */

class MobileTitleHeightAdjuster {
    constructor() {
        this.fontSize = 14; // CSS中设置的字体大小
        this.lineHeight = 1.5; // CSS中设置的行高
        this.containerPadding = 30; // tr的总内边距 (15px * 2)
        this.marginBottom = 8; // tableLeft的margin-bottom
        this.buttonHeight = 50; // 按钮区域的大概高度
        this.timeWidth = 60; // 时间标签的宽度
        
        // 绑定窗口大小变化事件
        window.addEventListener('resize', () => {
            this.adjustAllTitles();
        });
        
        // 监听表格变化
        this.observeTableChanges();
    }
    
    /**
     * 计算文本在指定宽度下的行数
     */
    calculateTextLines(text, containerWidth) {
        // 创建临时测量元素
        const measureElement = document.createElement('div');
        measureElement.style.cssText = `
            position: absolute;
            visibility: hidden;
            height: auto;
            width: ${containerWidth}px;
            font-size: ${this.fontSize}px;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            font-weight: 500;
            word-wrap: break-word;
            overflow-wrap: break-word;
            white-space: normal;
            line-height: ${this.lineHeight};
            padding: 0;
            margin: 0;
            border: none;
        `;
        measureElement.textContent = text;
        
        document.body.appendChild(measureElement);
        
        const height = measureElement.offsetHeight;
        const lineHeight = this.fontSize * this.lineHeight;
        const lines = Math.ceil(height / lineHeight);
        
        document.body.removeChild(measureElement);
        
        return Math.max(1, lines); // 至少1行
    }
    
    /**
     * 计算所需的底部内边距
     */
    calculateRequiredPadding(lines) {
        // 基础内边距
        const basePadding = 12;

        // 如果只有1行，使用默认内边距
        if (lines <= 1) {
            return basePadding;
        }

        // 多行时，需要增加底部内边距来容纳额外的行
        const extraLines = lines - 1;
        const extraHeight = extraLines * this.fontSize * this.lineHeight;

        return basePadding + extraHeight;
    }
    
    /**
     * 调整单个视频标题的背景高度
     */
    adjustSingleTitle(row) {
        // 只在移动端执行
        if (window.innerWidth > 768) return;
        
        const titleElement = row.querySelector('.video-title-text');
        const tableLeftElement = row.querySelector('.tableLeft');
        
        if (!titleElement || !tableLeftElement) return;
        
        const text = titleElement.textContent.trim();
        if (!text) return;
        
        // 获取容器宽度
        const containerWidth = tableLeftElement.offsetWidth - this.timeWidth;
        
        if (containerWidth <= 0) {
            // 如果容器还没有渲染完成，延迟执行
            setTimeout(() => this.adjustSingleTitle(row), 100);
            return;
        }
        
        // 计算文本行数
        const lines = this.calculateTextLines(text, containerWidth);

        // 计算所需的总高度
        const baseHeight = 12; // 基础顶部内边距
        const textHeight = lines * this.fontSize * this.lineHeight; // 文本高度
        const buttonHeight = 40; // 按钮区域高度
        const bottomPadding = 12; // 底部内边距
        const totalHeight = baseHeight + textHeight + this.marginBottom + buttonHeight + bottomPadding;

        // 直接设置行的最小高度，让背景自然扩展
        row.style.minHeight = totalHeight + 'px';
        row.style.height = 'auto';

        // 添加调试信息
        console.log(`📏 视频标题: "${text.substring(0, 30)}${text.length > 30 ? '...' : ''}"
        📐 容器宽度: ${containerWidth}px
        📊 计算行数: ${lines}
        📏 文本高度: ${textHeight}px
        📏 总高度: ${totalHeight}px`);

        // 为行添加一个标记，表示已经调整过
        row.setAttribute('data-height-adjusted', 'true');
    }
    
    /**
     * 调整所有视频标题的背景高度
     */
    adjustAllTitles() {
        // 只在移动端执行
        if (window.innerWidth > 768) return;
        
        const videoRows = document.querySelectorAll('#videosTable tr');
        
        if (videoRows.length === 0) return;
        
        console.log(`🔧 开始调整 ${videoRows.length} 个视频标题的背景高度...`);
        
        videoRows.forEach((row, index) => {
            // 延迟执行，确保DOM已完全渲染
            setTimeout(() => {
                this.adjustSingleTitle(row);
            }, index * 10); // 每个项目延迟10ms，避免阻塞
        });
    }
    
    /**
     * 监听表格变化
     */
    observeTableChanges() {
        const table = document.getElementById('videosTable');
        if (!table) return;
        
        const observer = new MutationObserver((mutations) => {
            let shouldAdjust = false;
            
            mutations.forEach((mutation) => {
                if (mutation.type === 'childList') {
                    // 有新的行被添加或删除
                    if (mutation.addedNodes.length > 0) {
                        mutation.addedNodes.forEach((node) => {
                            if (node.nodeType === Node.ELEMENT_NODE && node.tagName === 'TR') {
                                shouldAdjust = true;
                            }
                        });
                    }
                }
            });
            
            if (shouldAdjust) {
                // 延迟执行，确保DOM更新完成
                setTimeout(() => {
                    this.adjustAllTitles();
                }, 200);
            }
        });
        
        observer.observe(table, {
            childList: true,
            subtree: true
        });
        
        console.log('👀 表格变化监听器已启动');
    }
    
    /**
     * 手动触发调整（供外部调用）
     */
    triggerAdjustment() {
        setTimeout(() => {
            this.adjustAllTitles();
        }, 100);
    }

    /**
     * 调试函数：检查所有可能的背景样式
     */
    debugBackgroundStyles() {
        console.log('🔍 开始检查所有可能的背景样式...');

        const videoRows = document.querySelectorAll('#videosTable tr');

        videoRows.forEach((row, index) => {
            console.log(`\n📋 视频项 ${index + 1}:`);

            // 检查tr元素
            const trStyles = window.getComputedStyle(row);
            console.log(`  tr背景: ${trStyles.background}`);
            console.log(`  tr背景色: ${trStyles.backgroundColor}`);

            // 检查td元素
            const tds = row.querySelectorAll('td');
            tds.forEach((td, tdIndex) => {
                const tdStyles = window.getComputedStyle(td);
                console.log(`  td[${tdIndex}]背景: ${tdStyles.background}`);
                console.log(`  td[${tdIndex}]背景色: ${tdStyles.backgroundColor}`);
            });

            // 检查tableLeft元素
            const tableLeft = row.querySelector('.tableLeft');
            if (tableLeft) {
                const tableLeftStyles = window.getComputedStyle(tableLeft);
                console.log(`  .tableLeft背景: ${tableLeftStyles.background}`);
                console.log(`  .tableLeft背景色: ${tableLeftStyles.backgroundColor}`);
            }

            // 检查video-title-text元素
            const titleText = row.querySelector('.video-title-text');
            if (titleText) {
                const titleStyles = window.getComputedStyle(titleText);
                console.log(`  .video-title-text背景: ${titleStyles.background}`);
                console.log(`  .video-title-text背景色: ${titleStyles.backgroundColor}`);
            }

            // 检查所有子元素
            const allElements = row.querySelectorAll('*');
            allElements.forEach((el, elIndex) => {
                const styles = window.getComputedStyle(el);
                if (styles.backgroundColor !== 'rgba(0, 0, 0, 0)' && styles.backgroundColor !== 'transparent') {
                    console.log(`  子元素[${elIndex}] ${el.tagName}.${el.className}背景色: ${styles.backgroundColor}`);
                }
            });
        });
    }
}

// 创建全局实例
let mobileTitleHeightAdjuster = null;

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    mobileTitleHeightAdjuster = new MobileTitleHeightAdjuster();

    // 初始调整
    setTimeout(() => {
        mobileTitleHeightAdjuster.adjustAllTitles();

        // 调试：检查背景样式
        if (window.innerWidth <= 768) {
            setTimeout(() => {
                mobileTitleHeightAdjuster.debugBackgroundStyles();
            }, 2000);
        }
    }, 1000);

    console.log('📱 移动端标题高度调整器已初始化');
});

// 导出供其他脚本使用
window.MobileTitleHeightAdjuster = MobileTitleHeightAdjuster;
window.mobileTitleHeightAdjuster = mobileTitleHeightAdjuster;
