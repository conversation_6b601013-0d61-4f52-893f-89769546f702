/**
 * Streamly 2.0 - Modern Professional Theme
 * 现代化专业主题样式
 */

/* 全局重置和基础样式 */
* {
    box-sizing: border-box;
}

body {
    margin: 0;
    padding: 0;
    font-family: 'Inter', 'Segoe UI', 'Roboto', -apple-system, BlinkMacSystemFont, sans-serif;
    background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 25%, #16213e 50%, #0f3460 100%);
    color: #e0e0e0;
    overflow-x: hidden;
    min-height: 100vh;
}

/* 动态背景效果 */
body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background:
        linear-gradient(rgba(15, 15, 35, 0.85), rgba(26, 26, 46, 0.85)),
        url('https://images.unsplash.com/photo-1514320291840-2e0a9bf2a9ae?w=1920&auto=format&fit=crop&q=80') center/cover no-repeat;
    z-index: -2;
    animation: backgroundShift 30s ease-in-out infinite;
}

/* 备用背景图片 */
body.bg-studio::before {
    background:
        linear-gradient(rgba(15, 15, 35, 0.85), rgba(26, 26, 46, 0.85)),
        url('https://images.unsplash.com/photo-1640957454144-392c8b05d5a5?w=1920&auto=format&fit=crop&q=80') center/cover no-repeat;
}

body.bg-modern::before {
    background:
        linear-gradient(rgba(15, 15, 35, 0.85), rgba(26, 26, 46, 0.85)),
        url('https://images.unsplash.com/photo-1627407660893-fe01f60d44c4?w=1920&auto=format&fit=crop&q=80') center/cover no-repeat;
}

body.bg-mixing::before {
    background:
        linear-gradient(rgba(15, 15, 35, 0.85), rgba(26, 26, 46, 0.85)),
        url('https://images.unsplash.com/photo-1518972559570-7cc1309f3229?w=1920&auto=format&fit=crop&q=80') center/cover no-repeat;
}

body.bg-gradient::before {
    background:
        linear-gradient(135deg, rgba(15, 15, 35, 0.95), rgba(26, 26, 46, 0.95)),
        radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.15) 0%, transparent 60%),
        radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.12) 0%, transparent 60%),
        radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.15) 0%, transparent 60%);
}

@keyframes backgroundShift {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.8; }
}

/* 现代化头部导航 */
header {
    background: rgba(15, 15, 35, 0.95) !important;
    backdrop-filter: blur(20px);
    border-bottom: 1px solid rgba(79, 195, 247, 0.2);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
    height: 70px !important;
    display: flex !important;
    align-items: center;
    padding: 0 20px;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    transition: all 0.3s ease;
}

header:hover {
    background: rgba(15, 15, 35, 0.98);
    box-shadow: 0 6px 30px rgba(79, 195, 247, 0.2);
}

/* 桌面端头部行布局 */
.header-top-row {
    display: flex !important;
    align-items: center !important;
    gap: 20px !important;
    flex: 1 !important;
}

.header-buttons-row {
    display: flex !important;
    align-items: center !important;
    gap: 10px !important;
}

/* Logo 样式改进 */
#logo {
    height: 50px !important;
    margin: 0 !important;
    filter: drop-shadow(0 0 10px rgba(79, 195, 247, 0.3));
    transition: all 0.3s ease;
}

#logo:hover {
    filter: drop-shadow(0 0 15px rgba(79, 195, 247, 0.5));
    transform: scale(1.05);
}

/* 现代化搜索框 */
#inputBoxContainer {
    flex: 1;
    max-width: 600px;
    margin: 0 30px;
    position: relative;
}

/* 搜索输入包装器 */
.search-input-wrapper {
    position: relative !important;
    display: flex !important;
    align-items: center !important;
    width: 100% !important;
}

#inputBox {
    width: 100% !important;
    padding: 12px 20px !important;
    padding-right: 55px !important;
    border: 2px solid rgba(79, 195, 247, 0.3) !important;
    border-radius: 25px !important;
    background: rgba(255, 255, 255, 0.1) !important;
    color: #e0e0e0 !important;
    font-size: 16px !important;
    transition: all 0.3s ease !important;
    backdrop-filter: blur(10px);
}

#inputBox::placeholder {
    color: rgba(224, 224, 224, 0.6);
}

#inputBox:focus {
    outline: none !important;
    border-color: #4fc3f7 !important;
    background: rgba(255, 255, 255, 0.15) !important;
    box-shadow: 0 0 20px rgba(79, 195, 247, 0.3) !important;
    transform: translateY(-1px);
}

/* 搜索按钮样式 */
.search-btn {
    position: absolute !important;
    right: 5px !important;
    top: 50% !important;
    transform: translateY(-50%) !important;
    width: 40px !important;
    height: 40px !important;
    border: none !important;
    border-radius: 50% !important;
    background: linear-gradient(135deg, #4fc3f7, #29b6f6) !important;
    color: white !important;
    cursor: pointer !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    font-size: 16px !important;
    transition: all 0.3s ease !important;
    box-shadow: 0 2px 8px rgba(79, 195, 247, 0.3) !important;
}

.search-btn:hover {
    background: linear-gradient(135deg, #29b6f6, #0288d1) !important;
    transform: translateY(-50%) scale(1.05) !important;
    box-shadow: 0 4px 15px rgba(79, 195, 247, 0.5) !important;
}

.search-btn:active {
    transform: translateY(-50%) scale(0.95) !important;
    box-shadow: 0 2px 5px rgba(79, 195, 247, 0.4) !important;
}

.search-btn i {
    font-size: 14px !important;
}

/* 头部按钮样式 */
.headerButton, #saveButton {
    background: rgba(79, 195, 247, 0.1) !important;
    border: 1px solid rgba(79, 195, 247, 0.3) !important;
    color: #4fc3f7 !important;
    padding: 10px 15px !important;
    border-radius: 12px !important;
    margin-left: 10px !important;
    transition: all 0.3s ease !important;
    cursor: pointer !important;
    backdrop-filter: blur(10px);
}

.headerButton:hover, #saveButton:hover {
    background: rgba(79, 195, 247, 0.2) !important;
    border-color: #4fc3f7 !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 4px 15px rgba(79, 195, 247, 0.3) !important;
}

/* 主内容区域 */
#main {
    margin: 90px 20px 20px 20px !important;
    padding: 30px !important;
    padding-bottom: 450px !important; /* 为底部固定的footer留出空间 */
    background: rgba(255, 255, 255, 0.05) !important;
    border-radius: 20px !important;
    backdrop-filter: blur(20px) !important;
    border: 1px solid rgba(255, 255, 255, 0.1) !important;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3) !important;
    position: relative;
    min-height: 60vh;
}

/* 确保主内容区域不会过度遮挡背景 */
#main.zen {
    background: rgba(255, 255, 255, 0.03) !important;
}

/* 播放列表名称输入框 */
#playlistNameBox {
    background: rgba(255, 255, 255, 0.1) !important;
    border: 2px solid rgba(79, 195, 247, 0.3) !important;
    color: #e0e0e0 !important;
    border-radius: 15px !important;
    padding: 15px 20px !important;
    font-size: 1.8em !important;
    margin-bottom: 20px !important;
    transition: all 0.3s ease !important;
    backdrop-filter: blur(10px);
}

#playlistNameBox:focus {
    outline: none !important;
    border-color: #4fc3f7 !important;
    background: rgba(255, 255, 255, 0.15) !important;
    box-shadow: 0 0 20px rgba(79, 195, 247, 0.3) !important;
}

/* 表格样式现代化 */
table {
    background: rgba(255, 255, 255, 0.05) !important;
    border-radius: 15px !important;
    overflow: hidden !important;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2) !important;
    border: 1px solid rgba(255, 255, 255, 0.1) !important;
}

td {
    background: transparent !important; /* 测试：去掉td背景 */
    border-bottom: 1px solid rgba(255, 255, 255, 0.1) !important;
    padding: 15px !important;
    transition: all 0.3s ease !important;
}

td:hover {
    background: rgba(79, 195, 247, 0.1) !important;
    transform: translateX(5px);
}

.selected td {
    background: rgba(79, 195, 247, 0.2) !important;
    border-left: 4px solid #4fc3f7 !important;
}

/* 底部链接区域 */
#links {
    display: block !important;
    text-align: center !important;
    margin-top: 30px !important;
    padding: 20px !important;
    background: rgba(255, 255, 255, 0.05) !important;
    border-radius: 15px !important;
    border: 1px solid rgba(255, 255, 255, 0.1) !important;
}

#links a {
    color: #4fc3f7 !important;
    text-decoration: none !important;
    margin: 0 10px !important;
    padding: 8px 15px !important;
    border-radius: 8px !important;
    transition: all 0.3s ease !important;
    display: inline-block !important;
}

#links a:hover {
    background: rgba(79, 195, 247, 0.1) !important;
    transform: translateY(-2px) !important;
}

/* 浮动菜单现代化 */
.floatingMenu {
    background: rgba(15, 15, 35, 0.95) !important;
    backdrop-filter: blur(20px) !important;
    border: 1px solid rgba(79, 195, 247, 0.3) !important;
    border-radius: 20px !important;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5) !important;
    color: #e0e0e0 !important;
}

/* 搜索结果窗口特定定位 */
#searchResultsWindow {
    position: fixed !important;
    width: 90vw !important;
    max-width: 800px !important;
    max-height: calc(100vh - 150px) !important;
    z-index: 10000 !important;
    /* 移除固定的top和left，让窗口管理器控制位置 */
    /* 初始位置将由窗口管理器的centerWindow函数设置 */
}

.floatingMenu h2 {
    color: #4fc3f7 !important;
    border-bottom: 2px solid rgba(79, 195, 247, 0.3) !important;
    padding-bottom: 10px !important;
    cursor: move !important;
    user-select: none !important;
    transition: all 0.2s ease !important;
}

/* 标题栏悬停效果 */
.floatingMenu h2:hover {
    background: rgba(79, 195, 247, 0.15) !important;
    border-radius: 8px 8px 0 0 !important;
    transform: translateY(-1px) !important;
}

/* 拖拽时的标题栏样式 */
.floatingMenu.being-dragged h2 {
    background: rgba(79, 195, 247, 0.2) !important;
    border-radius: 8px 8px 0 0 !important;
    box-shadow: 0 2px 10px rgba(79, 195, 247, 0.3) !important;
}

/* 拖拽状态下的全局光标 */
body.window-dragging {
    cursor: move !important;
}

body.window-dragging * {
    cursor: move !important;
}

/* 移动端触摸拖拽样式 */
@media (max-width: 768px) {
    .floatingMenu h2 {
        /* 增加触摸区域 */
        padding: 15px 8px !important;
        /* 防止触摸时的高亮选择 */
        -webkit-touch-callout: none !important;
        -webkit-user-select: none !important;
        -khtml-user-select: none !important;
        -moz-user-select: none !important;
        -ms-user-select: none !important;
        user-select: none !important;
        /* 防止触摸时的点击延迟 */
        touch-action: none !important;
    }

    /* 触摸时的视觉反馈 */
    .floatingMenu h2:active {
        background: rgba(79, 195, 247, 0.25) !important;
        /* 移除transform，避免影响位置计算 */
        transition: background 0.1s ease !important;
    }

    /* 拖拽时的移动端样式 */
    .floatingMenu.being-dragged h2 {
        background: rgba(79, 195, 247, 0.3) !important;
        box-shadow: 0 4px 20px rgba(79, 195, 247, 0.4) !important;
    }

    /* 防止移动端拖拽时页面滚动 */
    body.window-dragging {
        overflow: hidden !important;
        /* 暂时移除position: fixed，看是否影响窗口位置 */
        width: 100% !important;
    }
}

.floatingMenuCloseButton {
    background: rgba(255, 82, 82, 0.1) !important;
    border: 1px solid rgba(255, 82, 82, 0.3) !important;
    color: #ff5252 !important;
    border-radius: 8px !important;
    padding: 5px 10px !important;
    transition: all 0.3s ease !important;
}

.floatingMenuCloseButton:hover {
    background: rgba(255, 82, 82, 0.2) !important;
    transform: scale(1.05) !important;
}

/* 设置界面专用样式 */
#settingsWindow {
    width: 500px !important;
    max-width: calc(100vw - 40px) !important;
    max-height: calc(100vh - 40px) !important;
    overflow-y: auto !important;
    position: fixed !important;
    top: 20px !important;
    left: 50% !important;
    transform: translateX(-50%) !important;
    z-index: 2147483647 !important;
    margin-top: 20px !important;
    margin-bottom: 20px !important;
}

#settingsWindow h2 {
    color: #4fc3f7 !important;
    text-align: center !important;
    margin-bottom: 20px !important;
    font-size: 1.5rem !important;
    border-bottom: 2px solid rgba(79, 195, 247, 0.3) !important;
    padding-bottom: 10px !important;
}

.settings-section {
    margin-bottom: 25px !important;
    padding: 20px !important;
    background: rgba(255, 255, 255, 0.05) !important;
    border-radius: 10px !important;
    border: 1px solid rgba(255, 255, 255, 0.1) !important;
}

.settings-section h3 {
    color: #81c784 !important;
    font-size: 1.2rem !important;
    margin-bottom: 15px !important;
    display: flex !important;
    align-items: center !important;
    gap: 8px !important;
}

.setting-item {
    display: flex !important;
    justify-content: space-between !important;
    align-items: center !important;
    margin-bottom: 15px !important;
    padding: 10px 0 !important;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1) !important;
    flex-wrap: wrap !important;
}

.setting-item:last-child {
    border-bottom: none !important;
    margin-bottom: 0 !important;
}

.setting-item label {
    color: #e0e0e0 !important;
    font-weight: 500 !important;
    flex: 1 !important;
    margin-right: 15px !important;
    min-width: 200px !important;
}

.setting-item input[type="checkbox"] {
    width: 18px !important;
    height: 18px !important;
    accent-color: #4fc3f7 !important;
    cursor: pointer !important;
}

.setting-item input[type="range"] {
    width: 120px !important;
    accent-color: #4fc3f7 !important;
    cursor: pointer !important;
}

.setting-item select {
    background: rgba(255, 255, 255, 0.1) !important;
    border: 1px solid rgba(79, 195, 247, 0.3) !important;
    border-radius: 5px !important;
    color: #e0e0e0 !important;
    padding: 5px 10px !important;
    cursor: pointer !important;
    min-width: 120px !important;
}

.setting-item select option {
    background: #1a1a2e !important;
    color: #e0e0e0 !important;
}

.setting-item small {
    display: block !important;
    color: #b0bec5 !important;
    font-size: 0.8rem !important;
    margin-top: 5px !important;
    font-style: italic !important;
    width: 100% !important;
}

.settings-button {
    background: linear-gradient(45deg, #4fc3f7, #29b6f6) !important;
    border: none !important;
    border-radius: 8px !important;
    color: white !important;
    padding: 8px 16px !important;
    margin: 5px !important;
    cursor: pointer !important;
    font-weight: 500 !important;
    transition: all 0.3s ease !important;
    font-size: 0.9rem !important;
}

.settings-button:hover {
    transform: translateY(-2px) !important;
    box-shadow: 0 4px 15px rgba(79, 195, 247, 0.3) !important;
}

.settings-button.danger {
    background: linear-gradient(45deg, #f44336, #d32f2f) !important;
}

.settings-button.danger:hover {
    box-shadow: 0 4px 15px rgba(244, 67, 54, 0.3) !important;
}

.about-info {
    background: rgba(255, 255, 255, 0.05) !important;
    padding: 15px !important;
    border-radius: 8px !important;
    border: 1px solid rgba(255, 255, 255, 0.1) !important;
}

.about-info p {
    color: #b0bec5 !important;
    margin: 5px 0 !important;
    font-size: 0.9rem !important;
}

.about-info a {
    color: #4fc3f7 !important;
    text-decoration: none !important;
}

.about-info a:hover {
    text-decoration: underline !important;
}

#volumeValue {
    color: #4fc3f7 !important;
    font-weight: 500 !important;
    min-width: 40px !important;
    text-align: center !important;
}

/* 设置界面动画 */
@keyframes slideInRight {
    from { transform: translateX(100%); opacity: 0; }
    to { transform: translateX(0); opacity: 1; }
}

@keyframes slideOutRight {
    from { transform: translateX(0); opacity: 1; }
    to { transform: translateX(100%); opacity: 0; }
}

@keyframes fadeInUp {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

/* 设置项悬停效果 */
.setting-item:hover {
    background: rgba(255, 255, 255, 0.05) !important;
    border-radius: 8px !important;
    transition: all 0.3s ease !important;
}

/* 设置界面滚动条样式 */
#settingsWindow::-webkit-scrollbar {
    width: 8px !important;
}

#settingsWindow::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1) !important;
    border-radius: 4px !important;
}

#settingsWindow::-webkit-scrollbar-thumb {
    background: rgba(79, 195, 247, 0.5) !important;
    border-radius: 4px !important;
}

#settingsWindow::-webkit-scrollbar-thumb:hover {
    background: rgba(79, 195, 247, 0.7) !important;
}

/* 响应式设计 */
@media (max-width: 768px) {
    #settingsWindow {
        width: 95vw !important;
        max-height: 90vh !important;
    }

    .setting-item {
        flex-direction: column !important;
        align-items: flex-start !important;
        gap: 10px !important;
    }

    .setting-item label {
        min-width: auto !important;
        margin-right: 0 !important;
    }

    .settings-section {
        padding: 15px !important;
    }
}

/* 播放列表管理样式 */
#playlistManagerWindow {
    width: 600px !important;
    max-width: calc(100vw - 40px) !important;
    max-height: calc(100vh - 40px) !important;
    position: fixed !important;
    top: 20px !important;
    left: 50% !important;
    transform: translateX(-50%) !important;
    z-index: 2147483647 !important;
    margin-top: 20px !important;
    margin-bottom: 20px !important;
}

.playlist-section {
    margin-bottom: 30px !important;
    padding: 20px !important;
    background: rgba(255, 255, 255, 0.05) !important;
    border-radius: 15px !important;
    border: 1px solid rgba(255, 255, 255, 0.1) !important;
}

.playlist-section h3 {
    color: #81c784 !important;
    font-size: 1.3rem !important;
    margin-bottom: 15px !important;
    border-bottom: 2px solid rgba(129, 199, 132, 0.3) !important;
    padding-bottom: 8px !important;
}

.create-playlist-form {
    display: flex !important;
    flex-direction: column !important;
    gap: 15px !important;
}

.create-playlist-form input,
.create-playlist-form textarea {
    background: rgba(255, 255, 255, 0.1) !important;
    border: 1px solid rgba(79, 195, 247, 0.3) !important;
    border-radius: 8px !important;
    color: #e0e0e0 !important;
    padding: 12px !important;
    font-size: 1rem !important;
    resize: vertical !important;
}

.create-playlist-form input:focus,
.create-playlist-form textarea:focus {
    outline: none !important;
    border-color: #4fc3f7 !important;
    box-shadow: 0 0 0 2px rgba(79, 195, 247, 0.2) !important;
}

.action-button {
    background: linear-gradient(45deg, #4fc3f7, #29b6f6) !important;
    border: none !important;
    border-radius: 8px !important;
    color: white !important;
    padding: 12px 20px !important;
    cursor: pointer !important;
    font-weight: 500 !important;
    transition: all 0.3s ease !important;
    display: inline-flex !important;
    align-items: center !important;
    gap: 8px !important;
}

.action-button:hover {
    transform: translateY(-2px) !important;
    box-shadow: 0 4px 15px rgba(79, 195, 247, 0.3) !important;
}

.action-button.secondary {
    background: rgba(255, 255, 255, 0.1) !important;
    border: 1px solid rgba(255, 255, 255, 0.3) !important;
}

.action-button.secondary:hover {
    background: rgba(255, 255, 255, 0.2) !important;
}

.playlists-container {
    max-height: 400px !important;
    overflow-y: auto !important;
}

.playlist-item {
    background: rgba(255, 255, 255, 0.05) !important;
    border: 1px solid rgba(255, 255, 255, 0.1) !important;
    border-radius: 10px !important;
    margin-bottom: 15px !important;
    transition: all 0.3s ease !important;
}

.playlist-item:hover {
    background: rgba(255, 255, 255, 0.08) !important;
    border-color: rgba(79, 195, 247, 0.3) !important;
}

.playlist-header {
    display: flex !important;
    justify-content: space-between !important;
    align-items: center !important;
    padding: 15px !important;
}

.playlist-info {
    flex: 1 !important;
}

.playlist-name {
    color: #4fc3f7 !important;
    font-size: 1.2rem !important;
    margin-bottom: 5px !important;
}

.playlist-meta {
    color: #b0bec5 !important;
    font-size: 0.9rem !important;
    margin-bottom: 5px !important;
}

.playlist-description {
    color: #e0e0e0 !important;
    font-size: 0.9rem !important;
    font-style: italic !important;
}

.playlist-actions {
    display: flex !important;
    gap: 10px !important;
}

.playlist-action-btn {
    background: rgba(79, 195, 247, 0.1) !important;
    border: 1px solid rgba(79, 195, 247, 0.3) !important;
    border-radius: 6px !important;
    color: #4fc3f7 !important;
    padding: 8px !important;
    cursor: pointer !important;
    transition: all 0.3s ease !important;
    width: 36px !important;
    height: 36px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
}

.playlist-action-btn:hover {
    background: rgba(79, 195, 247, 0.2) !important;
    transform: scale(1.1) !important;
}

.playlist-videos {
    border-top: 1px solid rgba(255, 255, 255, 0.1) !important;
    padding: 15px !important;
    max-height: 300px !important;
    overflow-y: auto !important;
}

.playlist-video-item {
    display: flex !important;
    align-items: center !important;
    gap: 15px !important;
    padding: 10px !important;
    border-radius: 8px !important;
    margin-bottom: 10px !important;
    background: rgba(255, 255, 255, 0.03) !important;
    transition: all 0.3s ease !important;
}

.playlist-video-item:hover {
    background: rgba(255, 255, 255, 0.08) !important;
}

.video-thumbnail {
    width: 60px !important;
    height: 45px !important;
    object-fit: cover !important;
    border-radius: 6px !important;
}

.video-info {
    flex: 1 !important;
}

.video-title {
    color: #e0e0e0 !important;
    font-weight: 500 !important;
    margin-bottom: 5px !important;
    font-size: 0.9rem !important;
    line-height: 1.4 !important;
    word-wrap: break-word !important;
    overflow-wrap: break-word !important;
    /* 确保背景能覆盖多行文本 */
    box-decoration-break: clone !important;
    -webkit-box-decoration-break: clone !important;
}

.video-duration {
    color: #b0bec5 !important;
    font-size: 0.8rem !important;
}

.remove-video-btn {
    background: rgba(244, 67, 54, 0.1) !important;
    border: 1px solid rgba(244, 67, 54, 0.3) !important;
    border-radius: 6px !important;
    color: #f44336 !important;
    padding: 6px !important;
    cursor: pointer !important;
    transition: all 0.3s ease !important;
    width: 30px !important;
    height: 30px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
}

.remove-video-btn:hover {
    background: rgba(244, 67, 54, 0.2) !important;
    transform: scale(1.1) !important;
}

.empty-state {
    text-align: center !important;
    color: #b0bec5 !important;
    font-style: italic !important;
    padding: 40px 20px !important;
}

/* 模态框样式 */
.modal {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    width: 100% !important;
    height: 100% !important;
    background: rgba(0, 0, 0, 0.7) !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    z-index: 10000 !important;
    cursor: pointer !important;
}

.modal[style*="display: none"] {
    display: none !important;
    visibility: hidden !important;
    opacity: 0 !important;
    pointer-events: none !important;
}

.modal[style*="display: flex"] {
    display: flex !important;
    visibility: visible !important;
    opacity: 1 !important;
    pointer-events: auto !important;
}

/* 创建播放列表模态框专用样式 */
#quickCreatePlaylistModal {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    width: 100vw !important;
    height: 100vh !important;
    background: rgba(0, 0, 0, 0.8) !important;
    display: flex !important;
    align-items: flex-start !important;
    justify-content: center !important;
    z-index: 2147483647 !important;
    padding: 20px !important;
    box-sizing: border-box !important;
    overflow-y: auto !important;
}

#quickCreatePlaylistModal.modal[style*="display: none"] {
    display: none !important;
}

#quickCreatePlaylistModal.modal[style*="display: flex"] {
    display: flex !important;
}

#quickCreatePlaylistModal .modal-content {
    background: rgba(15, 15, 35, 0.98) !important;
    border: 1px solid rgba(79, 195, 247, 0.3) !important;
    border-radius: 15px !important;
    width: 500px !important;
    max-width: calc(100vw - 40px) !important;
    max-height: calc(100vh - 40px) !important;
    overflow: hidden !important;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.6) !important;
    position: relative !important;
    z-index: 2147483647 !important;
    display: flex !important;
    flex-direction: column !important;
    margin-top: 20px !important;
    margin-bottom: 20px !important;
    /* 禁用拖拽缩放功能 */
    pointer-events: auto !important;
}

#quickCreatePlaylistModal .modal-header {
    flex-shrink: 0 !important;
    padding: 12px 20px !important;
    border-bottom: 1px solid rgba(79, 195, 247, 0.2) !important;
}

#quickCreatePlaylistModal .modal-body {
    flex: 1 !important;
    overflow-y: auto !important;
    padding: 15px 20px !important;
    padding-bottom: 20px !important;
    max-height: none !important;
    min-height: 0 !important;
}

#quickCreatePlaylistModal .modal-footer {
    flex-shrink: 0 !important;
    padding: 12px 20px !important;
    border-top: 1px solid rgba(79, 195, 247, 0.2) !important;
    background: rgba(15, 15, 35, 0.98) !important;
    border-radius: 0 0 15px 15px !important;
    position: sticky !important;
    bottom: 0 !important;
    z-index: 2147483647 !important;
}

/* 自定义滚动条样式 */
#quickCreatePlaylistModal .modal-body::-webkit-scrollbar {
    width: 8px !important;
}

#quickCreatePlaylistModal .modal-body::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.2) !important;
    border-radius: 4px !important;
}

#quickCreatePlaylistModal .modal-body::-webkit-scrollbar-thumb {
    background: rgba(79, 195, 247, 0.5) !important;
    border-radius: 4px !important;
}

#quickCreatePlaylistModal .modal-body::-webkit-scrollbar-thumb:hover {
    background: rgba(79, 195, 247, 0.7) !important;
}

/* Firefox滚动条样式 */
#quickCreatePlaylistModal .modal-body {
    scrollbar-width: thin !important;
    scrollbar-color: rgba(79, 195, 247, 0.5) rgba(0, 0, 0, 0.2) !important;
}

/* 确保不被窗口管理器影响 */
#quickCreatePlaylistModal .modal-content[data-enhanced] {
    border: 1px solid rgba(79, 195, 247, 0.3) !important;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5) !important;
}

/* 禁用拖拽缩放手柄 */
#quickCreatePlaylistModal .resize-handle,
#quickCreatePlaylistModal .window-drag-area,
#quickCreatePlaylistModal .window-title-bar {
    display: none !important;
}

/* 强制确保模态框在绝对最顶层 */
#quickCreatePlaylistModal,
#quickCreatePlaylistModal *,
#quickCreatePlaylistModal .modal-content,
#quickCreatePlaylistModal .modal-header,
#quickCreatePlaylistModal .modal-body,
#quickCreatePlaylistModal .modal-footer {
    z-index: 2147483647 !important;
    position: relative !important;
}

/* 覆盖任何可能的层级冲突 */
#quickCreatePlaylistModal {
    z-index: 2147483647 !important;
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    bottom: 0 !important;
}

/* 当模态框、播放列表管理器或设置窗口显示时，强制隐藏footer */
#quickCreatePlaylistModal[style*="display: flex"] ~ footer,
#quickCreatePlaylistModal[style*="display: block"] ~ footer,
#playlistManagerWindow[style*="display: block"] ~ footer,
#settingsWindow[style*="display: block"] ~ footer {
    display: none !important;
    visibility: hidden !important;
    opacity: 0 !important;
    z-index: -1 !important;
}

/* 简单有效的解决方案：确保模态框显示时，整个footer区域z-index降低 */
body:has(#quickCreatePlaylistModal[style*="display: flex"]) footer,
body:has(#quickCreatePlaylistModal[style*="display: flex"]) footer *,
#quickCreatePlaylistModal[style*="display: flex"] ~ footer,
#quickCreatePlaylistModal[style*="display: flex"] ~ footer * {
    z-index: 1 !important;
}

/* 备用方案：直接针对播放器元素 */
#quickCreatePlaylistModal[style*="display: flex"] ~ * #playlistInterface,
#quickCreatePlaylistModal[style*="display: flex"] ~ * #youtubeContainer,
#quickCreatePlaylistModal[style*="display: flex"] ~ * #playerStatus {
    z-index: 1 !important;
}

/* 强制确保弹窗底部按钮可见 */
#quickCreatePlaylistModal .modal-footer .action-button {
    min-height: 40px !important;
    padding: 8px 16px !important;
    margin: 0 5px !important;
    display: inline-flex !important;
    align-items: center !important;
    justify-content: center !important;
    white-space: nowrap !important;
    position: relative !important;
    z-index: 2147483647 !important;
}

/* 确保弹窗在小屏幕上的显示 */
@media (max-height: 800px) {
    #quickCreatePlaylistModal {
        align-items: flex-start !important;
        padding-top: 10px !important;
        padding-bottom: 10px !important;
    }

    #quickCreatePlaylistModal .modal-content {
        margin-top: 10px !important;
        margin-bottom: 10px !important;
        max-height: calc(100vh - 20px) !important;
    }
}



/* ===== 存储信息区域样式 ===== */

.storage-info-section {
    margin-top: 20px !important;
    margin-bottom: 10px !important;
    border-top: 1px solid rgba(79, 195, 247, 0.2) !important;
    padding-top: 15px !important;
}

.storage-info-header {
    display: flex !important;
    justify-content: space-between !important;
    align-items: center !important;
    cursor: pointer !important;
    padding: 8px 0 !important;
    border-radius: 6px !important;
    transition: background 0.2s ease !important;
}

.storage-info-header:hover {
    background: rgba(79, 195, 247, 0.1) !important;
}

.storage-info-header h4 {
    margin: 0 !important;
    color: #4fc3f7 !important;
    font-size: 14px !important;
    font-weight: 600 !important;
}

.info-toggle-btn {
    background: none !important;
    border: none !important;
    color: #4fc3f7 !important;
    cursor: pointer !important;
    padding: 4px !important;
    border-radius: 4px !important;
    transition: all 0.2s ease !important;
}

.info-toggle-btn:hover {
    background: rgba(79, 195, 247, 0.2) !important;
}

.info-toggle-btn i {
    transition: transform 0.3s ease !important;
}

.info-toggle-btn.expanded i {
    transform: rotate(180deg) !important;
}

.storage-info-content {
    padding: 10px 0 !important;
    overflow: visible !important;
    transition: all 0.3s ease !important;
}

.storage-info-content[style*="display: block"] {
    animation: slideDown 0.3s ease !important;
}

.storage-info-content > div {
    margin-bottom: 8px !important;
    padding: 8px !important;
    background: rgba(79, 195, 247, 0.05) !important;
    border-radius: 8px !important;
    border-left: 3px solid rgba(79, 195, 247, 0.3) !important;
}

.storage-info-content h5 {
    margin: 0 0 6px 0 !important;
    color: #e0e0e0 !important;
    font-size: 13px !important;
    font-weight: 600 !important;
}

.path-display {
    display: flex !important;
    align-items: center !important;
    gap: 8px !important;
    margin-bottom: 8px !important;
}

.path-display code {
    flex: 1 !important;
    background: rgba(0, 0, 0, 0.3) !important;
    color: #81c784 !important;
    padding: 8px 12px !important;
    border-radius: 6px !important;
    font-family: 'Courier New', monospace !important;
    font-size: 11px !important;
    word-break: break-all !important;
    border: 1px solid rgba(79, 195, 247, 0.2) !important;
}

.copy-path-btn {
    background: rgba(79, 195, 247, 0.2) !important;
    border: none !important;
    color: #4fc3f7 !important;
    padding: 8px !important;
    border-radius: 6px !important;
    cursor: pointer !important;
    transition: all 0.2s ease !important;
    min-width: 36px !important;
    height: 36px !important;
}

.copy-path-btn:hover {
    background: rgba(79, 195, 247, 0.3) !important;
    transform: scale(1.05) !important;
}

.storage-key-info {
    margin-top: 8px !important;
}

.storage-key-info small {
    color: #b0b0b0 !important;
    font-size: 11px !important;
}

.storage-key-info code {
    background: rgba(0, 0, 0, 0.2) !important;
    color: #ffb74d !important;
    padding: 2px 6px !important;
    border-radius: 3px !important;
    font-size: 11px !important;
}

.storage-limits ul,
.privacy-security ul {
    margin: 4px 0 0 0 !important;
    padding-left: 0 !important;
    list-style: none !important;
}

.storage-limits li,
.privacy-security li {
    margin-bottom: 2px !important;
    padding: 1px 0 !important;
    font-size: 12px !important;
    line-height: 1.2 !important;
    color: #d0d0d0 !important;
}

.security-list li.security-good {
    color: #81c784 !important;
}

.security-list li.security-warning {
    color: #ffb74d !important;
}

.backup-suggestion p {
    margin: 4px 0 !important;
    font-size: 12px !important;
    color: #d0d0d0 !important;
    line-height: 1.2 !important;
}

.backup-btn {
    background: rgba(76, 175, 80, 0.2) !important;
    border: 1px solid rgba(76, 175, 80, 0.3) !important;
    color: #81c784 !important;
    padding: 8px 12px !important;
    border-radius: 6px !important;
    cursor: pointer !important;
    font-size: 12px !important;
    transition: all 0.2s ease !important;
    display: flex !important;
    align-items: center !important;
    gap: 6px !important;
}

.backup-btn:hover {
    background: rgba(76, 175, 80, 0.3) !important;
    transform: translateY(-1px) !important;
}

/* 响应式调整 */
@media (max-width: 768px) {
    #quickCreatePlaylistModal {
        padding: 10px !important;
    }

    #quickCreatePlaylistModal .modal-content {
        width: calc(100vw - 20px) !important;
        max-height: calc(100vh - 20px) !important;
    }

    .storage-info-content > div {
        padding: 8px !important;
        margin-bottom: 12px !important;
    }

    .path-display {
        flex-direction: column !important;
        align-items: stretch !important;
    }

    .copy-path-btn {
        align-self: flex-end !important;
        width: auto !important;
    }

    .storage-info-content h5 {
        font-size: 12px !important;
    }

    .storage-limits li,
    .privacy-security li {
        font-size: 11px !important;
    }
}

/* 小屏幕高度调整 */
@media (max-height: 700px) {
    #quickCreatePlaylistModal .modal-content {
        max-height: 95vh !important;
    }

    #quickCreatePlaylistModal .modal-header {
        padding: 10px 15px !important;
    }

    #quickCreatePlaylistModal .modal-body {
        padding: 15px !important;
    }

    #quickCreatePlaylistModal .modal-footer {
        padding: 10px 15px !important;
    }

    .storage-info-content > div {
        margin-bottom: 10px !important;
        padding: 8px !important;
    }
}

/* 极小屏幕高度调整 */
@media (max-height: 500px) {
    #quickCreatePlaylistModal .modal-content {
        max-height: 98vh !important;
    }

    #quickCreatePlaylistModal .modal-header {
        padding: 8px 12px !important;
    }

    #quickCreatePlaylistModal .modal-body {
        padding: 10px 12px !important;
    }

    #quickCreatePlaylistModal .modal-footer {
        padding: 8px 12px !important;
    }

    .storage-info-content > div {
        margin-bottom: 8px !important;
        padding: 6px !important;
    }

    .storage-info-content h5 {
        font-size: 0.9rem !important;
        margin-bottom: 8px !important;
    }

    .storage-info-content ul {
        margin: 8px 0 !important;
    }

    .storage-info-content li {
        margin-bottom: 4px !important;
        font-size: 0.85rem !important;
    }
}

/* ===== 动画效果 ===== */

@keyframes slideDown {
    from {
        opacity: 0;
        max-height: 0;
        transform: translateY(-10px);
        padding-top: 0;
        padding-bottom: 0;
    }
    to {
        opacity: 1;
        max-height: 1000px;
        transform: translateY(0);
        padding-top: 15px;
        padding-bottom: 15px;
    }
}

@keyframes fadeInOut {
    0% {
        opacity: 0;
        transform: translate(-50%, -50%) scale(0.8);
    }
    20% {
        opacity: 1;
        transform: translate(-50%, -50%) scale(1);
    }
    80% {
        opacity: 1;
        transform: translate(-50%, -50%) scale(1);
    }
    100% {
        opacity: 0;
        transform: translate(-50%, -50%) scale(0.8);
    }
}

/* 复制按钮动画 */
.copy-path-btn:active {
    transform: scale(0.95) !important;
}

/* 备份按钮动画 */
.backup-btn:active {
    transform: translateY(0) scale(0.98) !important;
}

/* 存储信息区域悬停效果 */
.storage-info-content > div:hover {
    background: rgba(79, 195, 247, 0.08) !important;
    border-left-color: rgba(79, 195, 247, 0.5) !important;
    transition: all 0.2s ease !important;
}

.modal-content {
    background: rgba(15, 15, 35, 0.95) !important;
    backdrop-filter: blur(20px) !important;
    border: 1px solid rgba(79, 195, 247, 0.3) !important;
    border-radius: 15px !important;
    width: 400px !important;
    max-width: 90vw !important;
    max-height: 80vh !important;
    overflow: hidden !important;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5) !important;
    position: relative !important;
    z-index: 10001 !important;
    cursor: default !important;
    pointer-events: auto !important;
}

.modal-header {
    display: flex !important;
    justify-content: space-between !important;
    align-items: center !important;
    padding: 20px !important;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1) !important;
}

.modal-header h3 {
    color: #4fc3f7 !important;
    margin: 0 !important;
}

.modal-close {
    background: rgba(255, 82, 82, 0.1) !important;
    border: 1px solid rgba(255, 82, 82, 0.3) !important;
    color: #ff5252 !important;
    border-radius: 6px !important;
    padding: 6px 10px !important;
    cursor: pointer !important;
    transition: all 0.3s ease !important;
}

.modal-close:hover {
    background: rgba(255, 82, 82, 0.2) !important;
    transform: scale(1.05) !important;
}

.modal-body {
    padding: 20px !important;
    max-height: 400px !important;
    overflow-y: auto !important;
}

.modal-footer {
    padding: 20px !important;
    border-top: 1px solid rgba(255, 255, 255, 0.1) !important;
    text-align: right !important;
}

.playlist-selector-list {
    display: flex !important;
    flex-direction: column !important;
    gap: 10px !important;
}

.playlist-selector-item {
    display: flex !important;
    justify-content: space-between !important;
    align-items: center !important;
    padding: 15px !important;
    background: rgba(255, 255, 255, 0.05) !important;
    border: 1px solid rgba(255, 255, 255, 0.1) !important;
    border-radius: 10px !important;
    cursor: pointer !important;
    transition: all 0.3s ease !important;
}

.playlist-selector-item:hover {
    background: rgba(79, 195, 247, 0.1) !important;
    border-color: rgba(79, 195, 247, 0.3) !important;
    transform: translateX(5px) !important;
}

.playlist-selector-info {
    flex: 1 !important;
}

.playlist-selector-name {
    color: #e0e0e0 !important;
    font-weight: 500 !important;
    margin-bottom: 5px !important;
}

.playlist-selector-count {
    color: #b0bec5 !important;
    font-size: 0.9rem !important;
}

/* 搜索结果操作区域 */
#searchActions {
    background: rgba(255, 255, 255, 0.05) !important;
    border-top: 1px solid rgba(255, 255, 255, 0.1) !important;
    display: flex !important;
    justify-content: space-between !important;
    align-items: center !important;
}

#selectedCount {
    font-weight: 500 !important;
}

/* 视频复选框样式 */
.video-checkbox {
    background: rgba(255, 255, 255, 0.1) !important;
    border: 2px solid rgba(79, 195, 247, 0.5) !important;
    border-radius: 4px !important;
}

.video-checkbox:checked {
    background: #4fc3f7 !important;
    border-color: #4fc3f7 !important;
}

/* 播放列表管理按钮样式 */
#playlistManagerButton {
    background: rgba(79, 195, 247, 0.1) !important;
    border: 1px solid rgba(79, 195, 247, 0.3) !important;
    color: #4fc3f7 !important;
    border-radius: 8px !important;
    padding: 8px 12px !important;
    cursor: pointer !important;
    transition: all 0.3s ease !important;
    font-size: 1.2rem !important;
}

#playlistManagerButton:hover {
    background: rgba(79, 195, 247, 0.2) !important;
    transform: scale(1.05) !important;
}

/* 通用播放列表按钮样式 - 仅适用于非桌面端表格按钮 */
.addToPlaylistButton:not(.tableButtons .addToPlaylistButton) {
    color: #81c784 !important;
    margin-left: 5px !important;
    cursor: pointer !important;
    transition: all 0.3s ease !important;
    font-size: 1.1rem !important;
    position: relative !important;
    z-index: 999 !important;
    pointer-events: auto !important;
    display: inline-block !important;
    padding: 4px 6px !important;
    border-radius: 4px !important;
    background: rgba(129, 199, 132, 0.1) !important;
    border: 1px solid rgba(129, 199, 132, 0.2) !important;
}

.addToPlaylistButton:not(.tableButtons .addToPlaylistButton):hover {
    color: #4caf50 !important;
    transform: scale(1.2) !important;
    background: rgba(76, 175, 80, 0.2) !important;
    border-color: rgba(76, 175, 80, 0.4) !important;
    z-index: 1000 !important;
}

/* 桌面端表格按钮容器样式优化 */
@media (min-width: 769px) {
    .tableButtons {
        display: flex !important;
        gap: 12px !important;
        align-items: center !important;
        justify-content: flex-end !important; /* 右对齐 */
        margin-top: 0 !important; /* 移除上边距，与标题对齐 */
        pointer-events: auto !important;
        position: absolute !important; /* 绝对定位 */
        right: 10px !important; /* 右侧定位 */
        top: 50% !important; /* 垂直居中 */
        transform: translateY(-50%) !important; /* 精确垂直居中 */
        z-index: 100 !important;
        cursor: default !important;
        background: rgba(0, 0, 0, 0.7) !important; /* 增强背景 */
        padding: 6px 12px !important; /* 调整内边距 */
        border-radius: 25px !important; /* 圆角背景 */
        backdrop-filter: blur(10px) !important; /* 背景模糊 */
        border: 1px solid rgba(79, 195, 247, 0.3) !important; /* 边框 */
        height: auto !important; /* 自动高度 */
        width: auto !important; /* 自动宽度 */
    }
}

/* 桌面端按钮样式 */
@media (min-width: 769px) {
    .tableButtons span {
        background: rgba(79, 195, 247, 0.8) !important; /* 高亮背景 */
        border: 2px solid rgba(79, 195, 247, 1) !important; /* 明亮边框 */
        border-radius: 50% !important; /* 圆形按钮 */
        width: 36px !important;
        height: 36px !important;
        min-width: 36px !important;
        min-height: 36px !important;
        max-width: 36px !important;
        max-height: 36px !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        font-size: 14px !important;
        color: #ffffff !important; /* 白色图标 */
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5) !important; /* 文字阴影 */
        box-shadow: 0 2px 8px rgba(79, 195, 247, 0.4) !important; /* 发光效果 */
        transition: all 0.3s ease !important;
        cursor: pointer !important;
        pointer-events: auto !important;
        position: relative !important;
        z-index: 101 !important;
        margin: 0 !important;
        padding: 0 !important; /* 覆盖原始样式 */
        flex-shrink: 0 !important;
        box-sizing: border-box !important; /* 确保边框包含在尺寸内 */
    }

    /* 覆盖原始styles.css中的last-child样式 */
    .tableButtons *:last-child {
        padding-right: 0 !important;
        padding: 0 !important;
    }
}

/* 桌面端按钮悬停效果 */
@media (min-width: 769px) {
    .tableButtons span:hover {
        background: rgba(79, 195, 247, 1) !important; /* 完全不透明的背景 */
        border-color: rgba(255, 255, 255, 0.9) !important; /* 白色边框 */
        box-shadow: 0 4px 16px rgba(79, 195, 247, 0.6) !important; /* 增强发光 */
        transform: scale(1.1) !important; /* 轻微放大 */
    }

    /* 特定按钮颜色 */
    .tableButtons .autoplayButton {
        background: rgba(255, 152, 0, 0.8) !important;
        border-color: rgba(255, 152, 0, 1) !important;
        box-shadow: 0 2px 8px rgba(255, 152, 0, 0.4) !important;
    }

    .tableButtons .autoplayButton:hover {
        background: rgba(255, 152, 0, 1) !important;
        border-color: rgba(255, 255, 255, 0.9) !important;
        box-shadow: 0 4px 16px rgba(255, 152, 0, 0.6) !important;
    }

    .tableButtons .playButton {
        background: rgba(76, 175, 80, 0.8) !important;
        border-color: rgba(76, 175, 80, 1) !important;
        box-shadow: 0 2px 8px rgba(76, 175, 80, 0.4) !important;
    }

    .tableButtons .playButton:hover {
        background: rgba(76, 175, 80, 1) !important;
        border-color: rgba(255, 255, 255, 0.9) !important;
        box-shadow: 0 4px 16px rgba(76, 175, 80, 0.6) !important;
    }

    .tableButtons .removeButton {
        background: rgba(244, 67, 54, 0.8) !important;
        border-color: rgba(244, 67, 54, 1) !important;
        box-shadow: 0 2px 8px rgba(244, 67, 54, 0.4) !important;
    }

    .tableButtons .removeButton:hover {
        background: rgba(244, 67, 54, 1) !important;
        border-color: rgba(255, 255, 255, 0.9) !important;
        box-shadow: 0 4px 16px rgba(244, 67, 54, 0.6) !important;
    }

    .tableButtons .addToPlaylistButton,
    .tableButtons span.addToPlaylistButton,
    .tableButtons .fa-plus-circle.addToPlaylistButton {
        background: rgba(33, 150, 243, 0.8) !important; /* 蓝色背景 */
        border-color: rgba(33, 150, 243, 1) !important; /* 蓝色边框 */
        box-shadow: 0 2px 8px rgba(33, 150, 243, 0.4) !important; /* 蓝色发光 */
        width: 36px !important;
        height: 36px !important;
        min-width: 36px !important;
        min-height: 36px !important;
        max-width: 36px !important;
        max-height: 36px !important;
        border-radius: 50% !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        padding: 0 !important; /* 覆盖原始样式的padding-right */
        margin: 0 !important; /* 确保没有外边距 */
        box-sizing: border-box !important; /* 确保边框包含在尺寸内 */
    }

    .tableButtons .addToPlaylistButton:hover,
    .tableButtons span.addToPlaylistButton:hover,
    .tableButtons .fa-plus-circle.addToPlaylistButton:hover {
        background: rgba(33, 150, 243, 1) !important; /* 蓝色悬停背景 */
        border-color: rgba(255, 255, 255, 0.9) !important;
        box-shadow: 0 4px 16px rgba(33, 150, 243, 0.6) !important; /* 蓝色悬停发光 */
    }
}

/* 视频标题文本样式 */
.video-title-text {
    display: inline-block !important;
    cursor: move !important;
    flex: 1 !important;
    margin-right: 10px !important;
}

/* 表格左侧单元格样式 */
.tableLeft {
    position: relative !important;
    overflow: visible !important;
    z-index: 1 !important;
    padding-right: 150px !important;
    min-height: 40px !important;
    display: flex !important;
    align-items: center !important;
}

/* 确保拖拽手柄只在标题文本区域有效 */
.tableLeft .video-title-text {
    cursor: move !important;
}

.tableLeft .tableButtons {
    cursor: default !important;
}

/* 防止按钮区域被拖拽影响 */
.tableButtons,
.tableButtons *,
.autoplayButton,
.playButton,
.removeButton,
.addToPlaylistButton {
    -webkit-user-drag: none !important;
    -khtml-user-drag: none !important;
    -moz-user-drag: none !important;
    -o-user-drag: none !important;
    user-drag: none !important;
    pointer-events: auto !important;
    cursor: pointer !important;
}

/* jQuery UI sortable 特定样式覆盖 */
.ui-sortable .tableButtons {
    pointer-events: auto !important;
    cursor: default !important;
}

.ui-sortable .tableButtons span {
    pointer-events: auto !important;
    cursor: pointer !important;
}

/* 拖拽时的视觉反馈 */
.ui-sortable-helper {
    background: rgba(79, 195, 247, 0.1) !important;
    border: 1px solid rgba(79, 195, 247, 0.3) !important;
}

.ui-sortable-placeholder {
    background: rgba(79, 195, 247, 0.05) !important;
    border: 2px dashed rgba(79, 195, 247, 0.3) !important;
    visibility: visible !important;
}

/* 桌面端addToPlaylistButton特殊样式修正 - 确保与其他按钮完全一致 */
@media (min-width: 769px) {
    .tableButtons .fa-plus-circle.addToPlaylistButton,
    .tableButtons span.fa-plus-circle.addToPlaylistButton {
        z-index: 9999 !important;
        position: relative !important;
        pointer-events: auto !important;
        cursor: pointer !important;
        display: flex !important; /* 改为flex确保居中 */
        align-items: center !important;
        justify-content: center !important;
        width: 36px !important; /* 与其他按钮一致 */
        height: 36px !important; /* 与其他按钮一致 */
        min-width: 36px !important;
        min-height: 36px !important;
        max-width: 36px !important;
        max-height: 36px !important;
        text-align: center !important;
        line-height: 1 !important;
        border-radius: 50% !important; /* 圆形 */
        background: rgba(33, 150, 243, 0.8) !important; /* 蓝色背景 */
        border: 2px solid rgba(33, 150, 243, 1) !important; /* 蓝色边框 */
        box-shadow: 0 2px 8px rgba(33, 150, 243, 0.4) !important; /* 蓝色发光效果 */
        color: #ffffff !important; /* 白色图标 */
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5) !important; /* 文字阴影 */
        transition: all 0.3s ease !important;
        margin: 0 !important;
        padding: 0 !important;
        flex-shrink: 0 !important;
    }
}

/* 桌面端表格行样式 */
@media (min-width: 769px) {
    #videosTable tr {
        position: relative !important;
        z-index: 1 !important;
        height: 50px !important; /* 固定行高 */
    }

    #videosTable tr td {
        position: relative !important;
        z-index: 2 !important;
        overflow: visible !important;
        vertical-align: middle !important; /* 垂直居中 */
        padding: 8px 12px !important;
    }

    #videosTable tr td.tableLeft {
        padding-right: 200px !important; /* 为按钮留出空间 */
    }

    /* 视频标题样式 */
    .video-title-text {
        display: block !important;
        line-height: 1.4 !important;
        color: rgba(255, 255, 255, 0.9) !important;
        font-weight: 500 !important;
    }
}

/* 移除所有可能的覆盖元素 */
.addToPlaylistButton {
    isolation: isolate !important;
    will-change: transform !important;
}

.tableButtons {
    isolation: isolate !important;
}

/* 播放列表选择器样式 */
.playlist-selector {
    display: flex !important;
    align-items: center !important;
    gap: 10px !important;
    background: rgba(255, 255, 255, 0.05) !important;
    border: 1px solid rgba(79, 195, 247, 0.3) !important;
    border-radius: 12px !important;
    padding: 8px 12px !important;
    backdrop-filter: blur(10px) !important;
}

#currentPlaylistSelect {
    background: transparent !important;
    border: none !important;
    color: #e0e0e0 !important;
    font-size: 1rem !important;
    font-weight: 500 !important;
    cursor: pointer !important;
    outline: none !important;
    min-width: 200px !important;
    padding: 4px 8px !important;
}

#currentPlaylistSelect option {
    background: #1a1a2e !important;
    color: #e0e0e0 !important;
    padding: 8px !important;
}

#currentPlaylistSelect option:disabled {
    color: #666 !important;
    font-style: italic !important;
}

#newPlaylistBtn,
#saveQueueBtn,
#managePlaylistsBtn {
    background: rgba(79, 195, 247, 0.1) !important;
    border: 1px solid rgba(79, 195, 247, 0.3) !important;
    color: #4fc3f7 !important;
    border-radius: 8px !important;
    padding: 6px 10px !important;
    cursor: pointer !important;
    transition: all 0.3s ease !important;
    font-size: 1rem !important;
    min-width: 36px !important;
    height: 36px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
}

#newPlaylistBtn:hover,
#saveQueueBtn:hover,
#managePlaylistsBtn:hover {
    background: rgba(79, 195, 247, 0.2) !important;
    transform: scale(1.05) !important;
}

#saveQueueBtn {
    background: rgba(76, 175, 80, 0.1) !important;
    border-color: rgba(76, 175, 80, 0.3) !important;
    color: #4caf50 !important;
}

#saveQueueBtn:hover {
    background: rgba(76, 175, 80, 0.2) !important;
}

/* 快速创建表单样式 */
.quick-create-form {
    display: flex !important;
    flex-direction: column !important;
    gap: 15px !important;
}

.quick-create-form input,
.quick-create-form textarea {
    background: rgba(255, 255, 255, 0.1) !important;
    border: 1px solid rgba(79, 195, 247, 0.3) !important;
    border-radius: 8px !important;
    color: #e0e0e0 !important;
    padding: 12px !important;
    font-size: 1rem !important;
    resize: vertical !important;
    font-family: inherit !important;
}

.quick-create-form input:focus,
.quick-create-form textarea:focus {
    outline: none !important;
    border-color: #4fc3f7 !important;
    box-shadow: 0 0 0 2px rgba(79, 195, 247, 0.2) !important;
}

.quick-create-form input::placeholder,
.quick-create-form textarea::placeholder {
    color: #b0bec5 !important;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .playlist-selector {
        flex-wrap: wrap !important;
        gap: 8px !important;
    }

    #currentPlaylistSelect {
        min-width: 150px !important;
        font-size: 0.9rem !important;
    }

    #newPlaylistBtn,
    #saveQueueBtn,
    #managePlaylistsBtn {
        min-width: 32px !important;
        height: 32px !important;
        font-size: 0.9rem !important;
    }
}

/* 本地播放列表管理样式 */
.current-status {
    background: rgba(255, 255, 255, 0.05) !important;
    border-radius: 10px !important;
    padding: 15px !important;
    border: 1px solid rgba(255, 255, 255, 0.1) !important;
}

.status-item {
    display: flex !important;
    justify-content: space-between !important;
    align-items: center !important;
    margin-bottom: 10px !important;
}

.status-item:last-child {
    margin-bottom: 0 !important;
}

.status-label {
    color: #b0bec5 !important;
    font-weight: 500 !important;
}

.quick-actions {
    display: flex !important;
    flex-wrap: wrap !important;
    gap: 10px !important;
}

.quick-actions .action-button {
    flex: 1 !important;
    min-width: 140px !important;
}

.current-playlist {
    border-color: rgba(76, 175, 80, 0.5) !important;
    background: rgba(76, 175, 80, 0.05) !important;
}

.current-playlist .playlist-name {
    color: #4caf50 !important;
}

/* 播放列表项增强样式 */
.playlist-item {
    position: relative !important;
}

.playlist-item::before {
    content: '' !important;
    position: absolute !important;
    left: 0 !important;
    top: 0 !important;
    bottom: 0 !important;
    width: 3px !important;
    background: transparent !important;
    border-radius: 0 3px 3px 0 !important;
    transition: all 0.3s ease !important;
}

.current-playlist::before {
    background: #4caf50 !important;
}

.playlist-item:hover::before {
    background: rgba(79, 195, 247, 0.5) !important;
}

/* 状态指示器 */
#currentModeDisplay {
    font-weight: 600 !important;
    padding: 4px 8px !important;
    border-radius: 6px !important;
    background: rgba(79, 195, 247, 0.1) !important;
}

#currentQueueCount,
#localPlaylistCount {
    background: rgba(129, 199, 132, 0.1) !important;
    color: #81c784 !important;
    padding: 2px 8px !important;
    border-radius: 4px !important;
    font-weight: 600 !important;
    min-width: 24px !important;
    text-align: center !important;
}

/* 本地播放列表选择器样式 */
.local-playlist-selector-list {
    display: flex !important;
    flex-direction: column !important;
    gap: 10px !important;
    max-height: 400px !important;
    overflow-y: auto !important;
}

.local-playlist-selector-item {
    display: flex !important;
    justify-content: space-between !important;
    align-items: center !important;
    padding: 15px !important;
    background: rgba(255, 255, 255, 0.05) !important;
    border: 1px solid rgba(255, 255, 255, 0.1) !important;
    border-radius: 10px !important;
    cursor: pointer !important;
    transition: all 0.3s ease !important;
}

.local-playlist-selector-item:hover {
    background: rgba(79, 195, 247, 0.1) !important;
    border-color: rgba(79, 195, 247, 0.3) !important;
    transform: translateX(5px) !important;
}

.local-playlist-selector-info {
    flex: 1 !important;
}

.local-playlist-selector-name {
    color: #e0e0e0 !important;
    font-weight: 500 !important;
    margin-bottom: 5px !important;
    font-size: 1rem !important;
}

.local-playlist-selector-count {
    color: #4fc3f7 !important;
    font-size: 0.9rem !important;
    margin-bottom: 3px !important;
}

.local-playlist-selector-desc {
    color: #b0bec5 !important;
    font-size: 0.8rem !important;
    font-style: italic !important;
}

.local-playlist-selector-item i {
    color: #4fc3f7 !important;
    font-size: 1.2rem !important;
    transition: transform 0.3s ease !important;
}

.local-playlist-selector-item:hover i {
    transform: translateX(3px) !important;
}

/* 创建信息提示样式 */
.create-info {
    display: flex !important;
    align-items: center !important;
    gap: 8px !important;
    padding: 12px !important;
    background: rgba(79, 195, 247, 0.1) !important;
    border: 1px solid rgba(79, 195, 247, 0.3) !important;
    border-radius: 8px !important;
    color: #4fc3f7 !important;
    font-size: 0.9rem !important;
}

.create-info i {
    color: #4fc3f7 !important;
}

/* 空状态样式增强 */
.local-playlist-selector-list:empty::before {
    content: '暂无播放列表，请先创建一个播放列表' !important;
    display: block !important;
    text-align: center !important;
    color: #b0bec5 !important;
    font-style: italic !important;
    padding: 40px 20px !important;
}

/* 模态框滚动条样式 */
.local-playlist-selector-list::-webkit-scrollbar {
    width: 6px !important;
}

.local-playlist-selector-list::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1) !important;
    border-radius: 3px !important;
}

.local-playlist-selector-list::-webkit-scrollbar-thumb {
    background: rgba(79, 195, 247, 0.5) !important;
    border-radius: 3px !important;
}

.local-playlist-selector-list::-webkit-scrollbar-thumb:hover {
    background: rgba(79, 195, 247, 0.7) !important;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .local-playlist-selector-item {
        padding: 12px !important;
    }

    .local-playlist-selector-name {
        font-size: 0.9rem !important;
    }

    .local-playlist-selector-count,
    .local-playlist-selector-desc {
        font-size: 0.8rem !important;
    }
}

/* 搜索结果样式 */
#searchResults {
    max-height: 300px;
    overflow-y: auto;
}

#searchResults::-webkit-scrollbar {
    width: 8px;
}

#searchResults::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 4px;
}

#searchResults::-webkit-scrollbar-thumb {
    background: rgba(79, 195, 247, 0.5);
    border-radius: 4px;
}

#searchResults::-webkit-scrollbar-thumb:hover {
    background: rgba(79, 195, 247, 0.7);
}

/* 响应式设计 */
@media (max-width: 768px) {
    header {
        padding: 0 15px;
        height: 60px !important;
        flex-direction: row !important;
    }

    /* 保持单行布局 */
    .header-top-row {
        display: flex !important;
        align-items: center !important;
        gap: 15px !important;
        flex: 1 !important;
    }

    .header-buttons-row {
        display: flex !important;
        align-items: center !important;
        gap: 8px !important;
    }

    #inputBoxContainer {
        margin: 0 15px;
    }

    #main {
        margin: 80px 15px 15px 15px !important;
        padding: 20px !important;
    }

    .headerButton, #saveButton {
        padding: 8px 12px !important;
        margin-left: 8px !important;
    }

    #logo {
        height: 40px !important;
    }
}

/* 加载动画 */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

#main {
    animation: fadeInUp 0.8s ease-out;
}

/* 搜索进度指示器现代化 */
.spinner > div {
    background-color: #4fc3f7 !important;
    box-shadow: 0 0 10px rgba(79, 195, 247, 0.5) !important;
}

/* 表格行悬停效果增强 */
tr:hover td {
    background: rgba(79, 195, 247, 0.15) !important;
    box-shadow: 0 2px 10px rgba(79, 195, 247, 0.2) !important;
}

/* 按钮点击效果 */
.headerButton:active, #saveButton:active {
    transform: translateY(-1px) scale(0.98) !important;
}

/* 搜索框输入时的动画 */
#inputBox:not(:placeholder-shown) {
    background: rgba(79, 195, 247, 0.1) !important;
    border-color: #4fc3f7 !important;
}

/* 播放列表表格美化 */
table tr:first-child td {
    border-top: none !important;
}

table tr:last-child td {
    border-bottom: none !important;
}

/* 添加微妙的脉冲动画到活动元素 */
@keyframes pulse {
    0% { box-shadow: 0 0 0 0 rgba(79, 195, 247, 0.4); }
    70% { box-shadow: 0 0 0 10px rgba(79, 195, 247, 0); }
    100% { box-shadow: 0 0 0 0 rgba(79, 195, 247, 0); }
}

.playing {
    animation: pulse 2s infinite !important;
}

/* 改进滚动条样式 */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: linear-gradient(180deg, #4fc3f7, #29b6f6);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(180deg, #29b6f6, #4fc3f7);
}

/* 现代化底部播放器 */
footer {
    background: rgba(15, 15, 35, 0.95) !important;
    backdrop-filter: blur(20px) !important;
    border-top: 1px solid rgba(79, 195, 247, 0.2) !important;
    box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.3) !important;
    display: block !important;
    padding: 20px !important;
    min-height: 400px !important;
    height: auto !important;
    position: fixed !important;
    bottom: 0 !important;
    left: 0 !important;
    right: 0 !important;
    width: 100% !important;
    z-index: 1000 !important;
}

/* 播放器界面现代化 */
#playlistInterface {
    background: rgba(255, 255, 255, 0.05) !important;
    border-radius: 15px !important;
    border: 1px solid rgba(255, 255, 255, 0.1) !important;
    overflow: hidden !important;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2) !important;
    width: 50% !important;
    max-width: 600px !important;
    aspect-ratio: 16/9 !important;
    height: auto !important;
}

#youtubeContainer {
    border-radius: 15px !important;
    overflow: hidden !important;
    background: #000 !important;
    width: 100% !important;
    height: 85% !important;
    aspect-ratio: 16/9 !important;
}

/* 确保视频元素保持16:9比例 */
#youtube, #videoPlayer {
    width: 100% !important;
    height: 100% !important;
    aspect-ratio: 16/9 !important;
    object-fit: contain !important;
}

/* 视频预览现代化 - 电脑端保持两侧布局 */
#previousVideo, #nextVideo {
    background: rgba(255, 255, 255, 0.05) !important;
    border: 1px solid rgba(255, 255, 255, 0.1) !important;
    border-radius: 15px !important;
    backdrop-filter: blur(10px) !important;
    transition: all 0.3s ease !important;
    /* 保持原始的两侧定位 */
    position: absolute !important;
    top: 50% !important;
    transform: translateY(-50%) !important;
    width: 25% !important;
    height: 40% !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    cursor: pointer !important;
}

#previousVideo {
    left: 5% !important;
}

#nextVideo {
    right: 5% !important;
}

#previousVideo:hover, #nextVideo:hover {
    background: rgba(79, 195, 247, 0.1) !important;
    border-color: rgba(79, 195, 247, 0.3) !important;
    transform: translateY(-5px) !important;
    box-shadow: 0 8px 25px rgba(79, 195, 247, 0.2) !important;
}

/* 进度条现代化 */
#progressContainer {
    background: rgba(255, 255, 255, 0.1) !important;
    border: none !important;
    height: 6px !important;
    border-radius: 3px !important;
}

#progress {
    background: linear-gradient(90deg, #4fc3f7, #29b6f6) !important;
    box-shadow: 0 0 10px rgba(79, 195, 247, 0.5) !important;
}

/* 控制按钮现代化 */
#settings {
    background: rgba(255, 255, 255, 0.05) !important;
    border: 1px solid rgba(255, 255, 255, 0.1) !important;
    border-radius: 15px !important;
    backdrop-filter: blur(10px) !important;
    display: flex !important;
    align-items: center !important;
    justify-content: space-around !important;
    padding: 10px !important;
}

#settings span {
    color: #4fc3f7 !important;
    padding: 8px !important;
    border-radius: 8px !important;
    transition: all 0.3s ease !important;
}

#settings span:hover {
    background: rgba(79, 195, 247, 0.2) !important;
    transform: scale(1.1) !important;
}

#settings .selected {
    background: rgba(79, 195, 247, 0.3) !important;
    color: #fff !important;
}

/* 时间显示现代化 */
#currentTime, #videoTime {
    color: #4fc3f7 !important;
    font-weight: 500 !important;
    text-shadow: 0 0 10px rgba(79, 195, 247, 0.3) !important;
}

/* 欢迎消息样式 */
.welcome-message {
    text-align: center;
    padding: 40px 20px;
    background: rgba(79, 195, 247, 0.1);
    border: 1px solid rgba(79, 195, 247, 0.3);
    border-radius: 15px;
    margin: 20px 0;
}

.welcome-message h2 {
    color: #4fc3f7;
    margin-bottom: 15px;
    font-size: 1.8em;
}

.welcome-message p {
    color: #b0bec5;
    line-height: 1.6;
    margin-bottom: 10px;
}

/* Fork me 按钮隐藏在移动端 */
@media (max-width: 768px) {
    #forkme {
        display: none !important;
    }

    /* 移动端Footer - 改为随页面滚动 */
    footer {
        position: relative !important; /* 改为相对定位，随页面滚动 */
        bottom: auto !important;
        padding: 10px !important;
        min-height: 220px !important;
        margin-top: 20px !important; /* 与主内容区域保持间距 */
    }

    /* 移动端播放器界面调整 */
    #playlistInterface {
        width: 85% !important;
        max-width: 95vw !important;
        aspect-ratio: 16/9 !important;
        height: auto !important;
        position: absolute !important;
        left: 50% !important;
        top: 50% !important;
        transform: translate(-50%, -50%) !important;
        margin: 0 !important;
    }

    #youtubeContainer {
        height: 80% !important;
        position: relative !important;
        z-index: 1 !important;
    }

    /* 播放控制区域优化 */
    #currentVideoTiming {
        padding: 8px 0 !important;
        margin: 0 !important;
    }

    #settings {
        width: 120px !important;
        padding: 6px !important;
        font-size: 0.7em !important;
        height: 18px !important;
    }

    #settings span {
        padding: 1px !important;
        font-size: 1.1em !important;
    }

    /* 播放控制区域重新设计 */
    #settings {
        position: absolute !important;
        top: -45px !important;
        left: 50% !important;
        transform: translateX(-50%) !important;
        width: auto !important;
        height: 35px !important;
        background: rgba(0, 0, 0, 0.6) !important;
        border: 1px solid rgba(79, 195, 247, 0.3) !important;
        border-radius: 20px !important;
        padding: 5px 15px !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        gap: 15px !important;
        backdrop-filter: blur(10px) !important;
        z-index: 1002 !important;
    }

    #settings span {
        font-size: 18px !important;
        color: #4FC3F7 !important;
        cursor: pointer !important;
        padding: 5px !important;
        border-radius: 50% !important;
        transition: all 0.3s ease !important;
        background: rgba(79, 195, 247, 0.1) !important;
        width: 30px !important;
        height: 30px !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
    }

    #settings span:hover {
        background: rgba(79, 195, 247, 0.3) !important;
        transform: scale(1.1) !important;
    }

    /* 前进后退按钮优化 - 放在5个小按钮两侧 */
    #previousVideo, #nextVideo {
        width: 45px !important;
        height: 45px !important;
        padding: 3px !important;
        z-index: 1001 !important;
        position: absolute !important;
        top: -45px !important;
        background: rgba(79, 195, 247, 0.2) !important;
        border: 2px solid rgba(79, 195, 247, 0.5) !important;
        border-radius: 50% !important;
        color: #4FC3F7 !important;
        font-size: 20px !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        backdrop-filter: blur(10px) !important;
        transition: all 0.3s ease !important;
    }

    #previousVideo {
        left: calc(50% - 180px) !important;
    }

    #nextVideo {
        right: calc(50% - 180px) !important;
    }

    #previousVideo:hover, #nextVideo:hover {
        transform: scale(1.1) !important;
        background: rgba(79, 195, 247, 0.4) !important;
    }
}

/* ===== 桌面版播放控制区域重新设计 ===== */
@media (min-width: 769px) {
    /* 播放控制区域重新设计 */
    #settings {
        position: absolute !important;
        top: -50px !important;
        left: 50% !important;
        transform: translateX(-50%) !important;
        width: auto !important;
        height: 40px !important;
        background: rgba(0, 0, 0, 0.6) !important;
        border: 1px solid rgba(79, 195, 247, 0.3) !important;
        border-radius: 25px !important;
        padding: 5px 20px !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        gap: 20px !important;
        backdrop-filter: blur(10px) !important;
        z-index: 1002 !important;
        right: auto !important;
        bottom: auto !important;
    }

    #settings span {
        font-size: 20px !important;
        color: #4FC3F7 !important;
        cursor: pointer !important;
        padding: 6px !important;
        border-radius: 50% !important;
        transition: all 0.3s ease !important;
        background: rgba(79, 195, 247, 0.1) !important;
        width: 35px !important;
        height: 35px !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
    }

    #settings span:hover {
        background: rgba(79, 195, 247, 0.3) !important;
        transform: scale(1.1) !important;
        color: white !important;
    }

    /* 电脑端前进后退按钮 - 恢复到视频播放框两侧的大气布局 */
    #previousVideo, #nextVideo {
        /* 恢复原始的两侧定位，覆盖之前的顶端设置 */
        position: absolute !important;
        top: 50% !important;
        transform: translateY(-50%) !important;
        width: 25% !important;
        height: 40% !important;
        background: rgba(255, 255, 255, 0.05) !important;
        border: 1px solid rgba(255, 255, 255, 0.1) !important;
        border-radius: 15px !important;
        backdrop-filter: blur(10px) !important;
        transition: all 0.3s ease !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        cursor: pointer !important;
        z-index: 1001 !important;
        font-size: 2em !important;
        color: #fff !important;
    }

    #previousVideo {
        left: 5% !important;
    }

    #nextVideo {
        right: 5% !important;
    }

    #previousVideo:hover, #nextVideo:hover {
        transform: scale(1.15) !important;
        background: rgba(79, 195, 247, 0.4) !important;
        box-shadow: 0 4px 15px rgba(79, 195, 247, 0.3) !important;
    }
}

/* ===== 全面响应式设计 ===== */

/* 超小屏幕 (手机竖屏) */
@media (max-width: 480px) {
    /* 头部导航 - 重新设计为两行布局 */
    header {
        padding: 6px !important;
        height: auto !important;
        min-height: 70px !important;
        flex-direction: column !important;
        align-items: stretch !important;
        gap: 6px !important;
    }

    /* 第一行：Logo + 搜索框 */
    .header-top-row {
        display: flex !important;
        align-items: center !important;
        gap: 8px !important;
        width: 100% !important;
    }

    /* Logo区域 */
    #logo {
        height: 30px !important;
        margin: 0 !important;
        flex-shrink: 0 !important;
    }

    /* 搜索框容器 - 占据剩余空间到屏幕最右端 */
    #inputBoxContainer {
        margin: 0 !important;
        flex: 1 !important;
        width: 100% !important;
        max-width: none !important;
        min-width: 0 !important;
    }

    #inputBox {
        font-size: 14px !important;
        padding: 8px 40px 8px 12px !important;
        width: 100% !important;
        box-sizing: border-box !important;
        height: 30px !important;
    }

    .search-btn {
        width: 30px !important;
        height: 30px !important;
        right: 5px !important;
        font-size: 12px !important;
    }

    .search-btn i {
        font-size: 12px !important;
    }

    /* 第二行：按钮区域 - 居中排列 */
    .header-buttons-row {
        display: flex !important;
        justify-content: center !important;
        align-items: center !important;
        gap: 12px !important;
        width: 100% !important;
        padding: 4px 0 2px 0 !important;
        flex-wrap: wrap !important;
    }

    /* 头部按钮样式 */
    .headerButton, #saveButton, #playlistManagerButton, #backgroundSelectorBtn {
        padding: 8px 12px !important;
        margin: 0 !important;
        font-size: 14px !important;
        border-radius: 10px !important;
        min-width: 40px !important;
        height: 40px !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        background: rgba(79, 195, 247, 0.15) !important;
        border: 1px solid rgba(79, 195, 247, 0.4) !important;
        color: #4fc3f7 !important;
        transition: all 0.3s ease !important;
        cursor: pointer !important;
        box-shadow: 0 2px 8px rgba(79, 195, 247, 0.2) !important;
    }

    /* 按钮悬停效果 */
    .headerButton:hover, #saveButton:hover, #playlistManagerButton:hover, #backgroundSelectorBtn:hover {
        background: rgba(79, 195, 247, 0.25) !important;
        border-color: #4fc3f7 !important;
        transform: translateY(-1px) !important;
        box-shadow: 0 4px 12px rgba(79, 195, 247, 0.3) !important;
    }

    /* 隐藏不太重要的按钮在超小屏幕上 */
    #sbsButton {
        display: none !important;
    }

    /* 覆盖原始styles.css中的移动端样式 */
    #inputBoxContainer {
        width: auto !important;
        margin-left: 0 !important;
    }

    /* 播放列表选择器 - 优化移动端布局 */
    .playlist-selector {
        flex-direction: row !important;
        flex-wrap: wrap !important;
        gap: 6px !important;
        padding: 8px !important;
        justify-content: center !important;
        margin: 10px 0 !important;
    }

    #currentPlaylistSelect {
        min-width: calc(100% - 20px) !important;
        font-size: 14px !important;
        padding: 8px !important;
        margin-bottom: 6px !important;
        flex: 1 1 100% !important;
    }

    #newPlaylistBtn,
    #saveQueueBtn,
    #managePlaylistsBtn {
        min-width: calc(33.33% - 8px) !important;
        height: 36px !important;
        font-size: 12px !important;
        padding: 6px 8px !important;
        flex: 1 1 calc(33.33% - 8px) !important;
        text-align: center !important;
    }

    /* 主要内容区域 */
    .mainContent {
        padding: 10px !important;
        margin: 10px !important;
    }

    /* 主内容区域适应新的header高度 */
    #main {
        margin-top: 85px !important;
        padding: 10px 5px !important;
    }

    /* 移动端播放列表重新设计 */
    #videosTable {
        font-size: 12px !important;
    }

    #videosTable tr {
        display: block !important; /* 改为块级元素 */
        position: relative !important; /* 为绝对定位的时间标签提供参考 */
        margin-bottom: 15px !important;
        background: transparent !important; /* 测试：去掉黑色背景 */
        border-radius: 12px !important;
        /* 动态内边距：顶部和底部12px，左右12px */
        padding: 12px 12px 12px 12px !important;
        border: 1px solid rgba(79, 195, 247, 0.2) !important;
        /* 关键修复：让高度完全自适应内容 */
        height: auto !important;
        min-height: auto !important;
        max-height: none !important;
        overflow: visible !important;
        /* 确保背景能覆盖所有内容 */
        box-sizing: border-box !important;
    }

    #videosTable td {
        display: block !important; /* 改为块级元素 */
        width: 100% !important;
        padding: 0 !important;
        border: none !important;
    }

    .tableLeft {
        padding-right: 0 !important; /* 移除右边距 */
        font-size: 14px !important;
        position: relative !important;
        margin-bottom: 8px !important; /* 与按钮行保持间距 */
        /* 关键：让容器完全自适应内容高度 */
        height: auto !important;
        min-height: auto !important;
        max-height: none !important;
        overflow: visible !important;
        /* 使用flex布局确保内容正确排列 */
        display: flex !important;
        flex-direction: column !important;
        flex: 1 !important;
        /* 添加黑色背景，覆盖整个视频标题区域 */
        background: rgba(0, 0, 0, 0.6) !important;
        border-radius: 8px !important;
        padding: 8px !important;
    }

    /* 移动端选中状态样式 - 确保背景覆盖多行文本 */
    #videosTable tr.selected,
    #videosTable tr.selected .tableLeft,
    #videosTable tr.selected td {
        background: rgba(255, 131, 0, 0.4) !important;
        /* 确保背景覆盖所有内容，包括换行文本 */
        background-clip: padding-box !important;
        box-decoration-break: clone !important;
        -webkit-box-decoration-break: clone !important;
    }

    /* 移动端选中状态样式 - 简化版本 */
    #videosTable tr.selected .tableLeft,
    #videosTable tr.selected td {
        background: rgba(255, 131, 0, 0.4) !important;
    }

    /* 移动端特殊处理 - 确保背景完全覆盖多行文本 */
    @media (max-width: 768px) {
        /* 移动端表格行 - 确保高度完全自适应 */
        #videosTable tr {
            /* 移除任何高度限制 */
            height: auto !important;
            min-height: auto !important;
            max-height: none !important;
            /* 确保内边距足够包含所有内容 */
            padding: 15px !important;
        }
    }

    /* 视频标题样式 */
    .video-title-text {
        display: block !important;
        width: calc(100% - 60px) !important; /* 为时间标签留出空间 */
        font-weight: 500 !important;
        color: #e0e0e0 !important;
        line-height: 1.5 !important; /* 增加行高让文字更清晰 */
        margin-bottom: 8px !important;
        cursor: move !important;
        padding: 0 !important;
        word-wrap: break-word !important;
        overflow-wrap: break-word !important;
        /* 确保文字不会创建自己的背景层 */
        background: transparent !important;
        /* 确保文字能正常换行，不限制高度 */
        white-space: normal !important;
        height: auto !important;
        min-height: auto !important;
        max-height: none !important;
        /* 让文字自然流动，不限制父容器高度 */
        flex-shrink: 0 !important;
    }

    /* 移动端按钮容器重新设计 - 仅适用于移动端 */
    @media (max-width: 768px) {
        .tableButtons {
            position: static !important; /* 改为静态定位 */
            transform: none !important;
            background: none !important;
            padding: 12px 0 !important; /* 增加上下内边距确保垂直居中 */
            border-radius: 0 !important;
            display: flex !important;
            justify-content: center !important;
            align-items: center !important;
            gap: 12px !important; /* 适中的按钮间距 */
            margin-top: 8px !important;
            width: 100% !important;
            right: auto !important;
            top: auto !important;
            flex-wrap: nowrap !important; /* 防止换行，保持一行显示 */
            height: 60px !important; /* 增加容器高度确保对齐 */
            box-sizing: border-box !important;
            /* 强制覆盖内联样式 */
            left: auto !important;
            margin-left: 0 !important;
            margin-right: 0 !important;
        }
    }

    /* 移动端按钮样式 - 仅适用于移动端 */
    @media (max-width: 768px) {
        .tableButtons span {
            background: rgba(79, 195, 247, 0.4) !important; /* 大幅增加背景透明度 */
            border: 2px solid rgba(79, 195, 247, 0.8) !important; /* 增强边框可见度和厚度 */
            border-radius: 50% !important;
            width: 44px !important; /* 稍微增大尺寸 */
            height: 44px !important; /* 稍微增大尺寸 */
            min-width: 44px !important; /* 防止压缩 */
            min-height: 44px !important; /* 防止压缩 */
            max-width: 44px !important; /* 防止拉伸 */
            max-height: 44px !important; /* 防止拉伸 */
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;
            font-size: 16px !important;
            margin: 0 !important;
            padding: 0 !important; /* 确保无内边距 */
            transition: all 0.3s ease !important;
            cursor: pointer !important;
            touch-action: manipulation !important;
            flex-shrink: 0 !important; /* 防止收缩 */
            box-sizing: border-box !important; /* 包含边框在尺寸内 */
            /* 强制垂直对齐 */
            vertical-align: middle !important;
            line-height: 44px !important;
            /* 强制覆盖任何内联样式 */
            position: relative !important;
            top: 0 !important;
            left: 0 !important;
            right: 0 !important;
            bottom: 0 !important;
            transform: none !important;
        }
    }

    /* 移动端按钮悬停和特定颜色样式 - 仅适用于移动端 */
    @media (max-width: 768px) {
        .tableButtons span:hover {
            background: rgba(79, 195, 247, 0.6) !important;
            border-color: rgba(79, 195, 247, 1.0) !important;
            transform: scale(1.05) !important; /* 减小缩放避免布局跳动 */
        }

        /* 为不同按钮设置特定颜色，大幅增强可见度 */
        .tableButtons .autoplayButton {
            color: #ffb74d !important; /* 更亮的橙色 - 电台按钮 */
            background: rgba(255, 152, 0, 0.5) !important; /* 大幅增加背景透明度 */
            border-color: rgba(255, 152, 0, 0.8) !important; /* 增强边框 */
        }

        .tableButtons .autoplayButton:hover {
            background: rgba(255, 152, 0, 0.7) !important;
            border-color: rgba(255, 152, 0, 1.0) !important;
        }

        .tableButtons .playButton {
            color: #81c784 !important; /* 更亮的绿色 - 播放按钮 */
            background: rgba(76, 175, 80, 0.5) !important; /* 绿色背景透明度 */
            border-color: rgba(76, 175, 80, 0.8) !important; /* 绿色边框 */
        }

        .tableButtons .playButton:hover {
            background: rgba(76, 175, 80, 0.7) !important;
            border-color: rgba(76, 175, 80, 1.0) !important;
        }

        .tableButtons .removeButton {
            color: #ef5350 !important; /* 更亮的红色 - 删除按钮 */
            background: rgba(244, 67, 54, 0.5) !important; /* 大幅增加背景透明度 */
            border-color: rgba(244, 67, 54, 0.8) !important; /* 增强边框 */
        }

        .tableButtons .removeButton:hover {
            background: rgba(244, 67, 54, 0.7) !important;
            border-color: rgba(244, 67, 54, 1.0) !important;
        }

        .tableButtons .addToPlaylistButton {
            color: #64b5f6 !important; /* 更亮的蓝色 - 添加按钮 */
            background: rgba(33, 150, 243, 0.5) !important; /* 蓝色背景透明度 */
            border-color: rgba(33, 150, 243, 0.8) !important; /* 蓝色边框 */
        }

        .tableButtons .addToPlaylistButton:hover {
            background: rgba(33, 150, 243, 0.7) !important;
            border-color: rgba(33, 150, 243, 1.0) !important;
        }
    }

    /* 移动端按钮图标和对齐样式 - 仅适用于移动端 */
    @media (max-width: 768px) {
        /* 确保所有按钮图标居中对齐 */
        .tableButtons span i,
        .tableButtons span::before {
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;
            width: 100% !important;
            height: 100% !important;
            line-height: 1 !important;
            text-align: center !important;
        }

        /* 强制按钮水平对齐 - 覆盖所有可能的样式冲突 */
        .tableButtons span.autoplayButton,
        .tableButtons span.playButton,
        .tableButtons span.removeButton,
        .tableButtons span.addToPlaylistButton {
            /* 强制重置所有可能影响对齐的属性 */
            margin: 0 !important;
            padding: 0 !important;
            top: 0 !important;
            bottom: 0 !important;
            vertical-align: baseline !important;
            float: none !important;
            position: relative !important;
            transform: none !important;
            /* 确保基线对齐 */
            align-self: center !important;
            flex-basis: auto !important;
        }

        /* 强制覆盖内联样式中的margin-right */
        .tableButtons span[style*="margin-right"] {
            margin-right: 0 !important;
        }
    }

    /* 移动端按钮额外优化 */
    @media (max-width: 768px) {
        .tableButtons {
            gap: 10px !important; /* 移动端稍微减小间距 */
            padding: 10px 0 !important; /* 增加内边距确保对齐 */
            height: 55px !important; /* 调整容器高度 */
        }

        .tableButtons span {
            width: 42px !important; /* 保持与桌面端一致的尺寸 */
            height: 42px !important;
            min-width: 42px !important;
            min-height: 42px !important;
            max-width: 42px !important;
            max-height: 42px !important;
            font-size: 15px !important;
            line-height: 42px !important; /* 确保垂直居中 */
            /* 移动端也使用更亮的样式 */
            background: rgba(79, 195, 247, 0.4) !important;
            border: 2px solid rgba(79, 195, 247, 0.8) !important;
        }

        /* 移动端按钮特定颜色也更亮 */
        .tableButtons .autoplayButton {
            background: rgba(255, 152, 0, 0.5) !important;
            border-color: rgba(255, 152, 0, 0.8) !important;
        }

        .tableButtons .playButton {
            background: rgba(76, 175, 80, 0.5) !important;
            border-color: rgba(76, 175, 80, 0.8) !important;
        }

        .tableButtons .removeButton {
            background: rgba(244, 67, 54, 0.5) !important;
            border-color: rgba(244, 67, 54, 0.8) !important;
        }

        .tableButtons .addToPlaylistButton {
            background: rgba(33, 150, 243, 0.5) !important;
            border-color: rgba(33, 150, 243, 0.8) !important;
        }
    }

    /* 时间列样式调整 - 移除背景，只保留时间显示 */
    #videosTable td:last-child {
        text-align: right !important;
        font-size: 11px !important;
        color: #999 !important;
        position: absolute !important;
        top: 12px !important;
        right: 12px !important;
        background: rgba(0, 0, 0, 0.8) !important; /* 给时间文字一个小的背景 */
        padding: 2px 6px !important;
        border-radius: 4px !important;
        font-weight: 500 !important;
        pointer-events: none !important; /* 防止时间标签干扰拖拽 */
        z-index: 10 !important; /* 确保时间标签在背景之上 */
    }

    /* 移动端拖拽优化 */
    .ui-sortable-helper {
        background: rgba(79, 195, 247, 0.2) !important;
        border: 2px solid rgba(79, 195, 247, 0.5) !important;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3) !important;
        transform: rotate(2deg) !important;
    }

    .ui-sortable-placeholder {
        background: rgba(79, 195, 247, 0.1) !important;
        border: 2px dashed rgba(79, 195, 247, 0.4) !important;
        height: 80px !important;
        margin-bottom: 15px !important;
        border-radius: 12px !important;
    }



    /* 浮动窗口 */
    .floatingMenu {
        width: 95vw !important;
        max-width: 95vw !important;
        max-height: 90vh !important;
        margin: 5px !important;
        padding: 15px !important;
    }

    /* 搜索结果窗口移动端定位 */
    #searchResultsWindow {
        width: 95vw !important;
        max-width: 95vw !important;
        max-height: calc(100vh - 150px) !important;
        /* 移除固定top定位，让窗口管理器控制 */
    }

    .floatingMenuCloseButton {
        top: 10px !important;
        right: 10px !important;
        font-size: 18px !important;
    }

    /* 模态框 */
    .modal-content {
        width: 95vw !important;
        max-width: 95vw !important;
        margin: 10px !important;
    }

    .modal-header h3 {
        font-size: 16px !important;
    }

    .modal-body {
        padding: 15px !important;
    }

    .modal-footer {
        padding: 15px !important;
        flex-direction: column !important;
        gap: 10px !important;
    }

    .action-button {
        width: 100% !important;
        padding: 12px !important;
        font-size: 14px !important;
    }

    /* 播放列表项 */
    .playlist-item {
        margin-bottom: 10px !important;
    }

    .playlist-header {
        flex-direction: column !important;
        align-items: flex-start !important;
        gap: 10px !important;
        padding: 12px !important;
    }

    .playlist-actions {
        width: 100% !important;
        justify-content: space-around !important;
    }

    .playlist-action-btn {
        width: 40px !important;
        height: 40px !important;
        font-size: 14px !important;
    }

    /* 搜索结果 */
    .searchResult {
        padding: 8px !important;
        margin-bottom: 8px !important;
        height: 90px !important;
        min-height: 90px !important;
        max-height: 90px !important;
    }

    .searchResult .left {
        padding: 4px !important;
        height: 100% !important;
        display: flex !important;
        flex-direction: column !important;
        justify-content: space-between !important;
    }

    .searchResultTitle {
        font-size: 13px !important;
        line-height: 1.4 !important;
        -webkit-line-clamp: 2 !important;
        margin-bottom: 4px !important;
        height: auto !important;
        max-height: 36px !important; /* 2行 * 18px行高 */
        overflow: hidden !important;
    }

    .searchResultChannel {
        font-size: 11px !important;
        margin-bottom: 2px !important;
        line-height: 1.2 !important;
    }

    .searchResultPublished {
        font-size: 10px !important;
        line-height: 1.2 !important;
    }

    .searchResultDescription {
        font-size: 12px !important;
        line-height: 1.4 !important;
    }

    /* 移动端播放列表视频项样式 */
    .playlist-video-item {
        padding: 12px !important;
        margin-bottom: 10px !important;
        /* 确保背景能覆盖多行文本 */
        background-clip: padding-box !important;
        box-decoration-break: clone !important;
        -webkit-box-decoration-break: clone !important;
    }

    .playlist-video-item .video-title {
        font-size: 14px !important;
        line-height: 1.4 !important;
        margin-bottom: 6px !important;
        /* 确保文字换行时背景正确覆盖 */
        padding: 2px 0 !important;
        box-decoration-break: clone !important;
        -webkit-box-decoration-break: clone !important;
    }
}

/* 小屏幕 (手机横屏/小平板) */
@media (min-width: 481px) and (max-width: 768px) {
    /* 头部导航 - 保持单行布局 */
    header {
        padding: 0 12px !important;
        height: 60px !important;
        align-items: center !important;
        justify-content: space-between !important;
        flex-direction: row !important;
    }

    /* 小屏幕下恢复单行布局 */
    .header-top-row {
        display: flex !important;
        align-items: center !important;
        gap: 15px !important;
        flex: 1 !important;
    }

    .header-buttons-row {
        display: flex !important;
        align-items: center !important;
        gap: 8px !important;
    }

    #inputBoxContainer {
        margin: 0 12px !important;
        flex: 1 !important;
        max-width: calc(100vw - 280px) !important;
    }

    .search-btn {
        width: 36px !important;
        height: 36px !important;
        font-size: 14px !important;
    }

    .search-btn i {
        font-size: 12px !important;
    }

    /* 头部按钮优化 */
    .headerButton, #saveButton, #backgroundSelectorBtn {
        padding: 8px 10px !important;
        margin-left: 6px !important;
        font-size: 15px !important;
        min-width: 36px !important;
        height: 36px !important;
    }

    #playlistManagerButton {
        padding: 6px 8px !important;
        margin-left: 6px !important;
        font-size: 18px !important;
        min-width: 36px !important;
        height: 36px !important;
    }

    /* 播放列表选择器 */
    .playlist-selector {
        gap: 8px !important;
        padding: 8px !important;
        flex-wrap: wrap !important;
        justify-content: center !important;
    }

    #currentPlaylistSelect {
        min-width: calc(100% - 16px) !important;
        font-size: 15px !important;
        margin-bottom: 6px !important;
        flex: 1 1 100% !important;
    }

    #newPlaylistBtn,
    #saveQueueBtn,
    #managePlaylistsBtn {
        min-width: calc(33.33% - 8px) !important;
        height: 38px !important;
        font-size: 14px !important;
        flex: 1 1 calc(33.33% - 8px) !important;
    }

    /* 移动端主要内容 - 为播放器控制按钮留出空间 */
    #main {
        margin: 120px 15px 15px 15px !important; /* 增加顶部边距避免被控制按钮遮挡 */
        padding: 15px !important;
        padding-top: 30px !important; /* 内部顶部间距 */
        padding-bottom: 30px !important; /* 减少底部间距，因为Footer不再固定 */
    }

    .mainContent {
        padding: 15px !important;
        margin: 15px !important;
    }

    /* 小屏幕播放器调整 */
    #playlistInterface {
        width: 90% !important;
        max-width: 95vw !important;
        aspect-ratio: 16/9 !important;
        height: auto !important;
        position: relative !important;
        left: 50% !important;
        transform: translateX(-50%) !important;
        margin: 0 auto !important;
    }

    #youtubeContainer {
        height: 78% !important;
        position: relative !important;
        z-index: 1 !important;
    }

    /* 小屏幕Footer - 保持随页面滚动 */
    footer {
        position: relative !important; /* 保持相对定位 */
        bottom: auto !important;
        min-height: 200px !important;
        padding: 8px !important;
        margin-top: 20px !important;
    }

    /* 播放控制区域进一步优化 */
    #currentVideoTiming {
        padding: 6px 0 !important;
        font-size: 0.9em !important;
    }

    #settings {
        width: 110px !important;
        padding: 4px !important;
        font-size: 0.65em !important;
        height: 16px !important;
    }

    #settings span {
        padding: 0.5px !important;
        font-size: 1em !important;
    }

    /* 小屏幕播放控制区域调整 */
    #settings {
        position: absolute !important;
        top: -40px !important;
        left: 50% !important;
        transform: translateX(-50%) !important;
        width: auto !important;
        height: 30px !important;
        background: rgba(0, 0, 0, 0.7) !important;
        border: 1px solid rgba(79, 195, 247, 0.3) !important;
        border-radius: 15px !important;
        padding: 3px 12px !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        gap: 10px !important;
        backdrop-filter: blur(8px) !important;
        z-index: 1002 !important;
    }

    #settings span {
        font-size: 14px !important;
        color: #4FC3F7 !important;
        cursor: pointer !important;
        padding: 3px !important;
        border-radius: 50% !important;
        background: rgba(79, 195, 247, 0.1) !important;
        width: 24px !important;
        height: 24px !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
    }

    /* 前进后退按钮进一步优化 */
    #previousVideo, #nextVideo {
        width: 35px !important;
        height: 35px !important;
        padding: 2px !important;
        z-index: 1001 !important;
        position: absolute !important;
        top: -40px !important;
        background: rgba(79, 195, 247, 0.2) !important;
        border: 2px solid rgba(79, 195, 247, 0.5) !important;
        border-radius: 50% !important;
        color: #4FC3F7 !important;
        font-size: 16px !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        backdrop-filter: blur(8px) !important;
    }

    #previousVideo {
        left: calc(50% - 140px) !important;
    }

    #nextVideo {
        right: calc(50% - 140px) !important;
    }

    .tableLeft {
        padding-right: 140px !important;
    }

    .tableButtons {
        right: 8px !important;
        gap: 6px !important;
        /* 移动端样式已在上方定义，此处保持兼容 */
    }

    /* 浮动窗口 */
    .floatingMenu {
        width: 90vw !important;
        max-width: 90vw !important;
        max-height: 85vh !important;
    }

    /* 搜索结果窗口平板定位 */
    #searchResultsWindow {
        width: 85vw !important;
        max-width: 700px !important;
        max-height: calc(100vh - 180px) !important;
        /* 移除固定top定位，让窗口管理器控制 */
    }

    /* 小屏幕平板搜索结果样式 */
    .searchResult {
        height: 85px !important;
        min-height: 85px !important;
        max-height: 85px !important;
        padding: 8px !important;
    }

    .searchResult .left {
        padding: 4px !important;
    }

    .searchResultTitle {
        font-size: 14px !important;
        line-height: 1.4 !important;
        max-height: 38px !important;
    }

    /* 模态框 */
    .modal-content {
        width: 85vw !important;
        max-width: 500px !important;
    }

    .modal-footer {
        flex-direction: row !important;
        gap: 15px !important;
    }

    .action-button {
        flex: 1 !important;
        min-width: 120px !important;
    }
}

/* 中等屏幕 (平板) */
@media (min-width: 769px) and (max-width: 1024px) {
    /* 头部导航 - 保持单行布局 */
    header {
        padding: 0 20px !important;
        height: 60px !important;
        flex-direction: row !important;
    }

    /* 平板下保持单行布局 */
    .header-top-row {
        display: flex !important;
        align-items: center !important;
        gap: 20px !important;
        flex: 1 !important;
    }

    .header-buttons-row {
        display: flex !important;
        align-items: center !important;
        gap: 10px !important;
    }

    #inputBoxContainer {
        margin: 0 20px !important;
        max-width: 500px !important;
    }

    /* 播放列表选择器 */
    #currentPlaylistSelect {
        min-width: 220px !important;
    }

    /* 主要内容 */
    .mainContent {
        padding: 20px !important;
        margin: 20px !important;
    }

    /* 中等屏幕播放器调整 */
    #playlistInterface {
        width: 60% !important;
        max-width: 700px !important;
    }

    footer {
        min-height: 380px !important;
    }

    /* 浮动窗口 */
    .floatingMenu {
        width: 80vw !important;
        max-width: 700px !important;
        max-height: 80vh !important;
    }

    /* 搜索结果窗口平板定位 */
    #searchResultsWindow {
        width: 75vw !important;
        max-width: 750px !important;
        max-height: calc(100vh - 190px) !important;
        /* 移除固定top定位，让窗口管理器控制 */
    }

    /* 模态框 */
    .modal-content {
        width: 70vw !important;
        max-width: 600px !important;
    }
}

/* 大屏幕优化 */
@media (min-width: 1025px) {
    /* 确保大屏幕上的最佳体验 */
    #inputBoxContainer {
        max-width: 600px !important;
    }

    .floatingMenu {
        max-width: 800px !important;
    }

    /* 搜索结果窗口大屏幕定位 */
    #searchResultsWindow {
        width: 70vw !important;
        max-width: 900px !important;
        max-height: calc(100vh - 200px) !important;
        /* 移除固定top定位，让窗口管理器控制 */
    }

    /* 大屏幕播放器调整 */
    #playlistInterface {
        width: 55% !important;
        max-width: 800px !important;
    }

    footer {
        min-height: 420px !important;
    }

    .modal-content {
        max-width: 500px !important;
    }
}

/* 触摸设备优化 */
@media (hover: none) and (pointer: coarse) {
    /* 增大触摸目标，与移动端样式保持一致 */
    .tableButtons span {
        width: 42px !important;
        height: 42px !important;
        min-width: 42px !important;
        min-height: 42px !important;
        max-width: 42px !important;
        max-height: 42px !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
    }

    .playlist-action-btn {
        min-width: 44px !important;
        min-height: 44px !important;
    }

    .action-button {
        min-height: 44px !important;
        padding: 12px 16px !important;
    }

    /* 头部按钮触摸优化 */
    .headerButton, #saveButton, #playlistManagerButton {
        min-width: 44px !important;
        min-height: 44px !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
    }

    /* 播放器控制按钮触摸优化 */
    #previousVideo, #nextVideo {
        min-width: 50px !important;
        min-height: 50px !important;
        border-radius: 8px !important;
        background: rgba(0, 0, 0, 0.4) !important;
        backdrop-filter: blur(8px) !important;
        border: 1px solid rgba(255, 255, 255, 0.2) !important;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3) !important;
    }

    #settings span {
        min-width: 32px !important;
        min-height: 32px !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        border-radius: 6px !important;
        background: rgba(255, 255, 255, 0.1) !important;
        margin: 0 2px !important;
    }

    /* 进度条触摸优化 */
    #progressContainer {
        height: 8px !important;
        border-radius: 4px !important;
        cursor: pointer !important;
    }
}

    /* 移除悬停效果 */
    .tableButtons span:hover,
    .playlist-action-btn:hover,
    .action-button:hover,
    .headerButton:hover,
    #saveButton:hover,
    #playlistManagerButton:hover {
        transform: none !important;
    }

    /* 优化点击反馈 */
    .tableButtons span:active,
    .playlist-action-btn:active,
    .action-button:active,
    .headerButton:active,
    #saveButton:active,
    #playlistManagerButton:active {
        transform: scale(0.95) !important;
        transition: transform 0.1s ease !important;
    }
}

/* 横屏模式优化 */
@media (orientation: landscape) and (max-height: 500px) {
    header {
        height: 45px !important;
        padding: 0 15px !important;
    }

    .floatingMenu {
        max-height: 95vh !important;
        padding: 10px !important;
    }

    .modal-content {
        max-height: 95vh !important;
    }

    .modal-body {
        max-height: 300px !important;
        overflow-y: auto !important;
    }

    /* 横屏时优化播放列表选择器 */
    .playlist-selector {
        flex-direction: row !important;
        flex-wrap: wrap !important;
    }
}

/* 极小屏幕优化 (320px以下) */
@media (max-width: 320px) {
    /* 极小屏幕Footer - 保持随页面滚动 */
    footer {
        position: relative !important; /* 保持相对定位 */
        bottom: auto !important;
        min-height: 180px !important;
        padding: 6px !important;
        margin-top: 15px !important;
    }

    #playlistInterface {
        width: 95% !important;
        max-width: 98vw !important;
        position: relative !important;
        left: 50% !important;
        transform: translateX(-50%) !important;
        margin: 0 auto !important;
    }

    #youtubeContainer {
        height: 75% !important;
        position: relative !important;
        z-index: 1 !important;
    }

    #currentVideoTiming {
        padding: 4px 0 !important;
        font-size: 0.8em !important;
    }

    #settings {
        width: 100px !important;
        padding: 3px !important;
        font-size: 0.6em !important;
        height: 14px !important;
    }

    #previousVideo, #nextVideo {
        width: 10% !important;
        height: 20% !important;
        z-index: 3 !important;
        position: absolute !important;
        top: 50% !important;
        transform: translateY(-50%) !important;
    }

    #previousVideo {
        left: 10% !important;
    }

    #nextVideo {
        right: 10% !important;
    }
}

/* 高分辨率屏幕优化 */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    /* 确保在高分辨率屏幕上的清晰度 */
    .tableButtons span {
        font-size: 1.1rem !important;
    }

    .playlist-action-btn {
        font-size: 1rem !important;
    }

    /* 优化边框和阴影 */
    .floatingMenu,
    .modal-content {
        border-width: 0.5px !important;
    }
}

/* 超宽屏幕优化 */
@media (min-width: 1440px) {
    .mainContent {
        max-width: 1200px !important;
        margin: 0 auto !important;
    }

    header {
        max-width: 1200px !important;
        margin: 0 auto !important;
    }
}

/* 打印样式 */
@media print {
    header,
    .floatingMenu,
    .modal,
    .tableButtons,
    #forkme {
        display: none !important;
    }

    body {
        background: white !important;
        color: black !important;
    }

    #videosTable {
        color: black !important;
    }
}

/* ===== 稳定窗口管理器样式 ===== */

/* 拖拽区域样式 */
.window-drag-area {
    display: flex !important;
    align-items: center !important;
    gap: 8px !important;
    font-size: 14px !important;
    font-weight: 500 !important;
    color: #4fc3f7 !important;
    border-radius: 8px 8px 0 0 !important;
}

.window-drag-area:hover {
    background: rgba(79, 195, 247, 0.2) !important;
}

/* 缩放手柄样式 */
.resize-handle {
    transition: background 0.2s ease !important;
}

.resize-handle:hover {
    background: rgba(79, 195, 247, 0.3) !important;
}

/* 增强的窗口样式 */
[data-enhanced="true"] {
    border: 1px solid rgba(79, 195, 247, 0.3) !important;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2) !important;
    backdrop-filter: blur(10px) !important;
    border-radius: 12px !important;
    overflow: hidden !important;
}

/* 拖拽时的视觉反馈 */
[data-enhanced="true"].dragging {
    opacity: 0.9 !important;
    transform: scale(1.02) !important;
    transition: none !important;
}

/* 缩放时的视觉反馈 */
[data-enhanced="true"].resizing {
    transition: none !important;
}

/* 确保内容区域可滚动 */
[data-enhanced="true"] .floatingMenuContents,
[data-enhanced="true"] .modal-body {
    max-height: calc(100% - 60px) !important;
    overflow-y: auto !important;
}

/* 响应式优化 */
@media (max-width: 768px) {
    .window-drag-area {
        padding: 6px !important;
        font-size: 12px !important;
    }

    .resize-handle.resize-se {
        width: 20px !important;
        height: 20px !important;
    }

    .resize-handle.resize-s,
    .resize-handle.resize-e {
        background: rgba(79, 195, 247, 0.1) !important;
    }
}

/* 触摸设备优化 */
@media (hover: none) and (pointer: coarse) {
    .resize-handle.resize-se {
        width: 25px !important;
        height: 25px !important;
        background: rgba(79, 195, 247, 0.2) !important;
    }

    .resize-handle.resize-s {
        height: 12px !important;
        background: rgba(79, 195, 247, 0.1) !important;
    }

    .resize-handle.resize-e {
        width: 12px !important;
        background: rgba(79, 195, 247, 0.1) !important;
    }
}
